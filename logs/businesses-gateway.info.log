2025-06-03 14:10:36.557|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-03 14:10:36.577|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 29188 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-03 14:10:36.577|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-03 14:10:38.093|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-03 14:10:38.098|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-03 14:10:38.140|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-06-03 14:10:38.357|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-03 14:10:38.360|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-03 14:10:38.574|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=cba8101c-70b2-3117-9a4d-99779797c903
2025-06-03 14:10:38.665|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-03 14:10:38.670|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-03 14:10:46.888|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:10:50.921|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:10:54.968|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:10:59.000|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:10:59.001|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-03 14:11:03.026|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:11:07.063|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:11:07.063|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-03 14:11:07.112|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-03 14:11:07.594|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:11:07.599|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:11:07.630|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:11:07.751|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$6fa7fdb5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:11:07.771|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:11:07.772|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:11:07.775|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:11:11.100|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:11:15.126|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:11:19.128|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:11:23.129|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
