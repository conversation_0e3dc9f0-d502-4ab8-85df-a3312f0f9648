package cn.hydee.gateway.filter;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 过滤器基准测试
 * 
 * 用于测试优化前后的性能对比
 */
@SpringBootTest
@TestPropertySource(properties = {
    "spring.redis.host=localhost",
    "spring.redis.port=6379",
    "spring.redis.timeout=1000ms"
})
public class FilterBenchmarkTest {

    /**
     * 基准测试：模拟同步Redis操作的性能
     */
    @Test
    void benchmarkSyncRedisOperations() throws InterruptedException {
        System.out.println("=== 同步Redis操作基准测试 ===");
        
        int threadCount = 50;
        int operationsPerThread = 1000;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        AtomicLong totalTime = new AtomicLong(0);
        AtomicLong totalOperations = new AtomicLong(0);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    long threadStartTime = System.nanoTime();
                    
                    for (int j = 0; j < operationsPerThread; j++) {
                        // 模拟同步Redis操作
                        simulateSyncRedisOperation();
                        totalOperations.incrementAndGet();
                    }
                    
                    long threadEndTime = System.nanoTime();
                    totalTime.addAndGet(threadEndTime - threadStartTime);
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await(30, TimeUnit.SECONDS);
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        long totalTestTime = endTime - startTime;
        
        double avgOperationTime = totalTime.get() / (double) totalOperations.get() / 1_000_000.0; // ms
        double qps = totalOperations.get() * 1000.0 / totalTestTime;
        
        System.out.println("线程数: " + threadCount);
        System.out.println("每线程操作数: " + operationsPerThread);
        System.out.println("总操作数: " + totalOperations.get());
        System.out.println("总耗时: " + totalTestTime + " ms");
        System.out.println("平均操作时间: " + String.format("%.3f", avgOperationTime) + " ms");
        System.out.println("QPS: " + String.format("%.2f", qps));
        System.out.println();
    }

    /**
     * 基准测试：模拟响应式Redis操作的性能
     */
    @Test
    void benchmarkReactiveRedisOperations() throws InterruptedException {
        System.out.println("=== 响应式Redis操作基准测试 ===");
        
        int threadCount = 50;
        int operationsPerThread = 1000;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        AtomicLong totalTime = new AtomicLong(0);
        AtomicLong totalOperations = new AtomicLong(0);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    long threadStartTime = System.nanoTime();
                    
                    for (int j = 0; j < operationsPerThread; j++) {
                        // 模拟响应式Redis操作
                        simulateReactiveRedisOperation();
                        totalOperations.incrementAndGet();
                    }
                    
                    long threadEndTime = System.nanoTime();
                    totalTime.addAndGet(threadEndTime - threadStartTime);
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await(30, TimeUnit.SECONDS);
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        long totalTestTime = endTime - startTime;
        
        double avgOperationTime = totalTime.get() / (double) totalOperations.get() / 1_000_000.0; // ms
        double qps = totalOperations.get() * 1000.0 / totalTestTime;
        
        System.out.println("线程数: " + threadCount);
        System.out.println("每线程操作数: " + operationsPerThread);
        System.out.println("总操作数: " + totalOperations.get());
        System.out.println("总耗时: " + totalTestTime + " ms");
        System.out.println("平均操作时间: " + String.format("%.3f", avgOperationTime) + " ms");
        System.out.println("QPS: " + String.format("%.2f", qps));
        System.out.println();
    }

    /**
     * 对比测试：同步 vs 响应式
     */
    @Test
    void comparePerformance() throws InterruptedException {
        System.out.println("=== 性能对比测试 ===");
        
        // 测试同步操作
        long syncTime = measureSyncPerformance();
        
        // 测试响应式操作
        long reactiveTime = measureReactivePerformance();
        
        double improvement = (double) syncTime / reactiveTime;
        
        System.out.println("同步操作总耗时: " + syncTime + " ms");
        System.out.println("响应式操作总耗时: " + reactiveTime + " ms");
        System.out.println("性能提升倍数: " + String.format("%.2f", improvement) + "x");
        
        if (improvement > 1.0) {
            System.out.println("✅ 响应式实现性能更优");
        } else {
            System.out.println("⚠️ 需要进一步优化");
        }
    }

    /**
     * 高并发场景测试
     */
    @Test
    void highConcurrencyTest() throws InterruptedException {
        System.out.println("=== 高并发场景测试 ===");
        
        int[] threadCounts = {10, 50, 100, 200, 500};
        int operationsPerThread = 100;
        
        System.out.println("操作类型\t线程数\t总耗时(ms)\tQPS\t\t平均响应时间(ms)");
        System.out.println("------------------------------------------------------------");
        
        for (int threadCount : threadCounts) {
            // 测试同步操作
            long syncTime = measureConcurrentSync(threadCount, operationsPerThread);
            double syncQps = (threadCount * operationsPerThread * 1000.0) / syncTime;
            double syncAvgTime = syncTime / (double) (threadCount * operationsPerThread);
            
            // 测试响应式操作
            long reactiveTime = measureConcurrentReactive(threadCount, operationsPerThread);
            double reactiveQps = (threadCount * operationsPerThread * 1000.0) / reactiveTime;
            double reactiveAvgTime = reactiveTime / (double) (threadCount * operationsPerThread);
            
            System.out.printf("同步\t\t%d\t\t%d\t\t%.2f\t\t%.3f\n", 
                threadCount, syncTime, syncQps, syncAvgTime);
            System.out.printf("响应式\t\t%d\t\t%d\t\t%.2f\t\t%.3f\n", 
                threadCount, reactiveTime, reactiveQps, reactiveAvgTime);
            System.out.println();
        }
    }

    private long measureSyncPerformance() throws InterruptedException {
        int threadCount = 100;
        int operationsPerThread = 100;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        simulateSyncRedisOperation();
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await(30, TimeUnit.SECONDS);
        executor.shutdown();
        
        return System.currentTimeMillis() - startTime;
    }

    private long measureReactivePerformance() throws InterruptedException {
        int threadCount = 100;
        int operationsPerThread = 100;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        simulateReactiveRedisOperation();
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await(30, TimeUnit.SECONDS);
        executor.shutdown();
        
        return System.currentTimeMillis() - startTime;
    }

    private long measureConcurrentSync(int threadCount, int operationsPerThread) throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        simulateSyncRedisOperation();
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await(30, TimeUnit.SECONDS);
        executor.shutdown();
        
        return System.currentTimeMillis() - startTime;
    }

    private long measureConcurrentReactive(int threadCount, int operationsPerThread) throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        simulateReactiveRedisOperation();
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await(30, TimeUnit.SECONDS);
        executor.shutdown();
        
        return System.currentTimeMillis() - startTime;
    }

    /**
     * 模拟同步Redis操作
     * 包括网络延迟和阻塞等待
     */
    private void simulateSyncRedisOperation() {
        try {
            // 模拟网络延迟 + 序列化/反序列化时间
            Thread.sleep(2); // 2ms延迟
            
            // 模拟CPU密集型操作（序列化）
            for (int i = 0; i < 1000; i++) {
                Math.random();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 模拟响应式Redis操作
     * 非阻塞，更高效的资源利用
     */
    private void simulateReactiveRedisOperation() {
        // 响应式操作不阻塞线程
        // 模拟异步处理的CPU时间
        for (int i = 0; i < 500; i++) { // 更少的CPU时间
            Math.random();
        }
        
        // 在实际场景中，这里会是异步的网络调用
        // 不会阻塞当前线程
    }
}
