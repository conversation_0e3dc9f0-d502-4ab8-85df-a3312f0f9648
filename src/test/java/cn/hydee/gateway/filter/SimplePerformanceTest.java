package cn.hydee.gateway.filter;

import org.junit.jupiter.api.Test;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 简化的性能测试
 * 
 * 模拟同步和响应式操作的性能差异
 */
public class SimplePerformanceTest {

    @Test
    void testSyncVsReactivePerformance() throws InterruptedException {
        System.out.println("=== Spring Cloud Gateway 过滤器性能优化测试 ===\n");
        
        // 测试参数
        int threadCount = 100;
        int operationsPerThread = 1000;
        
        // 测试同步操作
        long syncTime = measureSyncOperations(threadCount, operationsPerThread);
        
        // 测试响应式操作
        long reactiveTime = measureReactiveOperations(threadCount, operationsPerThread);
        
        // 计算性能提升
        double improvement = (double) syncTime / reactiveTime;
        
        System.out.println("=== 测试结果 ===");
        System.out.println("测试场景: " + threadCount + " 个线程，每线程 " + operationsPerThread + " 次操作");
        System.out.println("同步操作总耗时: " + syncTime + " ms");
        System.out.println("响应式操作总耗时: " + reactiveTime + " ms");
        System.out.println("性能提升: " + String.format("%.2f", improvement) + "x");
        
        if (improvement > 1.0) {
            System.out.println("✅ 响应式实现性能更优");
        } else {
            System.out.println("⚠️ 需要进一步优化");
        }
        
        // 计算QPS
        long totalOperations = (long) threadCount * operationsPerThread;
        double syncQps = totalOperations * 1000.0 / syncTime;
        double reactiveQps = totalOperations * 1000.0 / reactiveTime;
        
        System.out.println("\n=== QPS 对比 ===");
        System.out.println("同步操作 QPS: " + String.format("%.2f", syncQps));
        System.out.println("响应式操作 QPS: " + String.format("%.2f", reactiveQps));
        System.out.println("QPS 提升: " + String.format("%.2f", reactiveQps / syncQps) + "x");
    }

    @Test
    void testHighConcurrencyScenario() throws InterruptedException {
        System.out.println("\n=== 高并发场景测试 ===");
        
        int[] threadCounts = {10, 50, 100, 200, 500};
        int operationsPerThread = 100;
        
        System.out.println("线程数\t同步耗时(ms)\t响应式耗时(ms)\t性能提升");
        System.out.println("--------------------------------------------");
        
        for (int threadCount : threadCounts) {
            long syncTime = measureSyncOperations(threadCount, operationsPerThread);
            long reactiveTime = measureReactiveOperations(threadCount, operationsPerThread);
            double improvement = (double) syncTime / reactiveTime;
            
            System.out.printf("%d\t\t%d\t\t\t%d\t\t\t%.2fx\n", 
                threadCount, syncTime, reactiveTime, improvement);
        }
    }

    @Test
    void testRedisOperationSimulation() throws InterruptedException {
        System.out.println("\n=== Redis 操作模拟测试 ===");
        
        int threadCount = 50;
        int operationsPerThread = 500;
        
        // 模拟多个Redis操作的场景
        long syncMultiRedisTime = measureSyncMultiRedisOperations(threadCount, operationsPerThread);
        long reactiveMultiRedisTime = measureReactiveMultiRedisOperations(threadCount, operationsPerThread);
        
        double improvement = (double) syncMultiRedisTime / reactiveMultiRedisTime;
        
        System.out.println("场景: 每次请求包含 3 个 Redis 操作（用户验证、权限检查、资源查询）");
        System.out.println("同步多Redis操作耗时: " + syncMultiRedisTime + " ms");
        System.out.println("响应式多Redis操作耗时: " + reactiveMultiRedisTime + " ms");
        System.out.println("性能提升: " + String.format("%.2f", improvement) + "x");
        
        // 计算平均每次请求的处理时间
        long totalRequests = (long) threadCount * operationsPerThread;
        double syncAvgTime = (double) syncMultiRedisTime / totalRequests;
        double reactiveAvgTime = (double) reactiveMultiRedisTime / totalRequests;
        
        System.out.println("\n平均每次请求处理时间:");
        System.out.println("同步方式: " + String.format("%.3f", syncAvgTime) + " ms");
        System.out.println("响应式方式: " + String.format("%.3f", reactiveAvgTime) + " ms");
    }

    private long measureSyncOperations(int threadCount, int operationsPerThread) throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        simulateSyncRedisOperation();
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await(30, TimeUnit.SECONDS);
        executor.shutdown();
        
        return System.currentTimeMillis() - startTime;
    }

    private long measureReactiveOperations(int threadCount, int operationsPerThread) throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        simulateReactiveRedisOperation();
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await(30, TimeUnit.SECONDS);
        executor.shutdown();
        
        return System.currentTimeMillis() - startTime;
    }

    private long measureSyncMultiRedisOperations(int threadCount, int operationsPerThread) throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        // 模拟过滤器中的多个Redis操作
                        simulateSyncRedisOperation(); // 用户验证
                        simulateSyncRedisOperation(); // 权限检查
                        simulateSyncRedisOperation(); // 资源查询
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await(60, TimeUnit.SECONDS);
        executor.shutdown();
        
        return System.currentTimeMillis() - startTime;
    }

    private long measureReactiveMultiRedisOperations(int threadCount, int operationsPerThread) throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        // 模拟响应式链式操作
                        simulateReactiveRedisOperation(); // 用户验证
                        simulateReactiveRedisOperation(); // 权限检查  
                        simulateReactiveRedisOperation(); // 资源查询
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await(60, TimeUnit.SECONDS);
        executor.shutdown();
        
        return System.currentTimeMillis() - startTime;
    }

    /**
     * 模拟同步Redis操作
     * 包括网络延迟和线程阻塞
     */
    private void simulateSyncRedisOperation() {
        try {
            // 模拟网络延迟 + 序列化/反序列化时间
            Thread.sleep(2); // 2ms延迟，模拟Redis网络调用
            
            // 模拟CPU密集型操作（序列化/反序列化）
            for (int i = 0; i < 1000; i++) {
                Math.random();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 模拟响应式Redis操作
     * 非阻塞，更高效的资源利用
     */
    private void simulateReactiveRedisOperation() {
        // 响应式操作不阻塞线程
        // 模拟异步处理的CPU时间（更少的CPU时间，因为没有线程切换开销）
        for (int i = 0; i < 500; i++) { // 更少的CPU时间
            Math.random();
        }
        
        // 在实际场景中，这里会是异步的网络调用
        // 不会阻塞当前线程，允许处理更多请求
    }
}
