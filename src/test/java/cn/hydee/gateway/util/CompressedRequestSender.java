package cn.hydee.gateway.util;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Scanner;

/**
 * 压缩请求发送器
 * 
 * 用于发送GZIP压缩的HTTP请求到网关
 * 
 * <AUTHOR> (moatkon)
 * @date 2025-06-03
 */
public class CompressedRequestSender {

    private static final String GATEWAY_URL = "http://localhost:9000/dev-test/forBusinessGateway/1";

    /**
     * 发送压缩请求
     * 
     * @param jsonData 原始JSON数据
     * @return 响应内容
     */
    public static String sendCompressedRequest(String jsonData) {
        try {
            // 1. 压缩数据
            byte[] compressedData = GZIPUtils.compress(jsonData);
            String compressedBody = new String(compressedData, StandardCharsets.ISO_8859_1);
            
            // 2. 创建连接
            URL url = new URL(GATEWAY_URL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            // 3. 设置请求方法和头部
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("huditCompressed", "1");  // 关键标识
            connection.setRequestProperty("Accept-Encoding", "gzip");
            connection.setRequestProperty("User-Agent", "GZIPRequestSender/1.0");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            
            // 4. 发送压缩数据
            try (OutputStream os = connection.getOutputStream()) {
                os.write(compressedBody.getBytes(StandardCharsets.UTF_8));
                os.flush();
            }
            
            // 5. 读取响应
            int responseCode = connection.getResponseCode();
            StringBuilder response = new StringBuilder();
            
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(
                    responseCode >= 200 && responseCode < 300 
                        ? connection.getInputStream() 
                        : connection.getErrorStream(),
                    StandardCharsets.UTF_8))) {
                
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line).append("\n");
                }
            }
            
            System.out.println("响应状态码: " + responseCode);
            System.out.println("响应头: " + connection.getHeaderFields());
            System.out.println("响应内容: " + response.toString());
            
            return response.toString();
            
        } catch (Exception e) {
            System.err.println("发送请求失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 交互式测试工具
     */
    public static void interactiveTest() {
        Scanner scanner = new Scanner(System.in);
        
        System.out.println("=== GZIP压缩请求测试工具 ===");
        System.out.println("目标URL: " + GATEWAY_URL);
        System.out.println();
        
        while (true) {
            System.out.println("请选择操作:");
            System.out.println("1. 发送预定义测试请求");
            System.out.println("2. 输入自定义JSON");
            System.out.println("3. 测试大数据量请求");
            System.out.println("4. 退出");
            System.out.print("请输入选择 (1-4): ");
            
            String choice = scanner.nextLine().trim();
            
            switch (choice) {
                case "1":
                    sendPredefinedRequests();
                    break;
                case "2":
                    sendCustomRequest(scanner);
                    break;
                case "3":
                    sendLargeDataRequest();
                    break;
                case "4":
                    System.out.println("退出测试工具");
                    return;
                default:
                    System.out.println("无效选择，请重新输入");
            }
            
            System.out.println();
        }
    }

    /**
     * 发送预定义的测试请求
     */
    private static void sendPredefinedRequests() {
        String[] testRequests = {
            """
            {
                "merCode": "TEST001",
                "userId": "user123",
                "action": "login",
                "timestamp": "2025-06-03T15:30:00"
            }
            """,
            """
            {
                "merCode": "HYDEE001",
                "userId": "user456",
                "action": "order",
                "orderData": {
                    "productId": "P001",
                    "productName": "测试商品",
                    "quantity": 2,
                    "price": 99.99
                },
                "timestamp": "2025-06-03T15:31:00"
            }
            """,
            """
            {
                "merCode": "GATEWAY001",
                "userId": "user789",
                "action": "query",
                "queryParams": {
                    "startDate": "2025-06-01",
                    "endDate": "2025-06-03",
                    "status": "active"
                },
                "timestamp": "2025-06-03T15:32:00"
            }
            """
        };

        for (int i = 0; i < testRequests.length; i++) {
            System.out.println("发送测试请求 " + (i + 1) + ":");
            System.out.println("原始JSON: " + testRequests[i].trim());
            
            try {
                // 显示压缩信息
                byte[] originalBytes = testRequests[i].getBytes(StandardCharsets.UTF_8);
                byte[] compressedBytes = GZIPUtils.compress(testRequests[i]);
                
                System.out.println("原始大小: " + originalBytes.length + " bytes");
                System.out.println("压缩后大小: " + compressedBytes.length + " bytes");
                System.out.println("压缩比: " + String.format("%.2f%%", 
                    (1.0 - (double)compressedBytes.length / originalBytes.length) * 100));
                
                // 发送请求
                String response = sendCompressedRequest(testRequests[i]);
                
            } catch (Exception e) {
                System.err.println("请求失败: " + e.getMessage());
            }
            
            System.out.println("---");
        }
    }

    /**
     * 发送自定义请求
     */
    private static void sendCustomRequest(Scanner scanner) {
        System.out.println("请输入JSON数据 (输入空行结束):");
        StringBuilder jsonBuilder = new StringBuilder();
        String line;
        
        while (!(line = scanner.nextLine()).isEmpty()) {
            jsonBuilder.append(line).append("\n");
        }
        
        String jsonData = jsonBuilder.toString().trim();
        if (!jsonData.isEmpty()) {
            System.out.println("发送自定义请求:");
            sendCompressedRequest(jsonData);
        } else {
            System.out.println("未输入有效的JSON数据");
        }
    }

    /**
     * 发送大数据量请求测试
     */
    private static void sendLargeDataRequest() {
        System.out.println("生成大数据量测试请求...");
        
        StringBuilder largeJson = new StringBuilder();
        largeJson.append("{\n");
        largeJson.append("  \"merCode\": \"LARGE_DATA_TEST\",\n");
        largeJson.append("  \"userId\": \"user_large_test\",\n");
        largeJson.append("  \"action\": \"bulk_data\",\n");
        largeJson.append("  \"data\": [\n");
        
        // 生成1000条测试数据
        for (int i = 0; i < 1000; i++) {
            largeJson.append("    {\n");
            largeJson.append("      \"id\": ").append(i).append(",\n");
            largeJson.append("      \"name\": \"测试数据项_").append(i).append("\",\n");
            largeJson.append("      \"description\": \"这是第").append(i).append("条测试数据，用于验证大数据量压缩传输的效果\",\n");
            largeJson.append("      \"value\": ").append(Math.random() * 1000).append(",\n");
            largeJson.append("      \"timestamp\": \"2025-06-03T15:").append(String.format("%02d", i % 60)).append(":00\"\n");
            largeJson.append("    }");
            if (i < 999) largeJson.append(",");
            largeJson.append("\n");
        }
        
        largeJson.append("  ],\n");
        largeJson.append("  \"timestamp\": \"2025-06-03T15:30:00\"\n");
        largeJson.append("}");
        
        String jsonData = largeJson.toString();
        System.out.println("大数据量请求生成完成，数据大小: " + jsonData.getBytes().length + " bytes");
        
        sendCompressedRequest(jsonData);
    }

    public static void main(String[] args) {
        if (args.length > 0 && "interactive".equals(args[0])) {
            interactiveTest();
        } else {
            // 默认发送一个简单的测试请求
            String testJson = """
                {
                    "merCode": "TEST001",
                    "userId": "user123",
                    "action": "test",
                    "message": "这是一个GZIP压缩测试请求",
                    "timestamp": "2025-06-03T15:30:00"
                }
                """;
            
            System.out.println("=== 发送GZIP压缩测试请求 ===");
            sendCompressedRequest(testJson);
            
            System.out.println("\n提示: 运行 'java CompressedRequestSender interactive' 进入交互模式");
        }
    }
}
