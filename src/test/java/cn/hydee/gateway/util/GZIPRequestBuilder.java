package cn.hydee.gateway.util;

import org.junit.jupiter.api.Test;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * GZIP请求构建器
 * 
 * 用于构建可以被 BodygzipFilter 正确处理的压缩请求
 * 
 * <AUTHOR> (moatkon)
 * @date 2025-06-03
 */
public class GZIPRequestBuilder {

    /**
     * 构建压缩的请求体
     * 
     * @param originalJson 原始JSON字符串
     * @return 压缩后的请求体（ISO-8859-1编码的字符串）
     */
    public static String buildCompressedBody(String originalJson) {
        try {
            // 1. 使用GZIPUtils压缩原始JSON
            byte[] compressedBytes = GZIPUtils.compress(originalJson);
            
            // 2. 转换为ISO-8859-1编码的字符串（这是BodygzipFilter期望的格式）
            String compressedBody = new String(compressedBytes, StandardCharsets.ISO_8859_1);
            
            System.out.println("原始JSON: " + originalJson);
            System.out.println("原始大小: " + originalJson.getBytes().length + " bytes");
            System.out.println("压缩后大小: " + compressedBytes.length + " bytes");
            System.out.println("压缩比: " + String.format("%.2f%%", 
                (1.0 - (double)compressedBytes.length / originalJson.getBytes().length) * 100));
            
            return compressedBody;
        } catch (IOException e) {
            throw new RuntimeException("压缩请求体失败", e);
        }
    }

    /**
     * 构建完整的HTTP请求头
     * 
     * @return 包含压缩标识的请求头
     */
    public static HttpHeaders buildCompressedHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("huditCompressed", "1");  // 关键：标识这是压缩请求
        headers.set("Accept-Encoding", "gzip");
        return headers;
    }

    /**
     * 验证压缩和解压过程
     */
    @Test
    public void testCompressionDecompression() {
        // 测试数据
        String originalJson = """
            {
                "userId": "12345",
                "userName": "张三",
                "email": "<EMAIL>",
                "phone": "13800138000",
                "address": "北京市朝阳区某某街道某某号",
                "orderItems": [
                    {
                        "productId": "P001",
                        "productName": "商品1",
                        "quantity": 2,
                        "price": 99.99
                    },
                    {
                        "productId": "P002", 
                        "productName": "商品2",
                        "quantity": 1,
                        "price": 199.99
                    }
                ],
                "totalAmount": 399.97,
                "orderTime": "2025-06-03T15:30:00",
                "remark": "这是一个测试订单，包含中文字符和特殊符号！@#$%^&*()"
            }
            """;

        try {
            // 1. 压缩
            String compressedBody = buildCompressedBody(originalJson);
            
            // 2. 模拟BodygzipFilter的解压过程
            String decompressed = GZIPUtils.uncompressToString(
                compressedBody.getBytes(StandardCharsets.ISO_8859_1)
            );
            
            // 3. 验证
            System.out.println("\n=== 验证结果 ===");
            System.out.println("解压后内容: " + decompressed);
            System.out.println("内容是否一致: " + originalJson.trim().equals(decompressed.trim()));
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 生成curl命令示例
     */
    @Test
    public void generateCurlCommand() {
        String originalJson = """
            {
                "merCode": "TEST001",
                "userId": "user123",
                "action": "test",
                "data": "这是测试数据"
            }
            """;

        String compressedBody = buildCompressedBody(originalJson);
        
        // 将压缩后的内容进行Base64编码，便于在curl中使用
        String base64Encoded = Base64.getEncoder().encodeToString(
            compressedBody.getBytes(StandardCharsets.ISO_8859_1)
        );
        
        System.out.println("\n=== CURL命令示例 ===");
        System.out.println("curl -X POST http://localhost:9000/dev-test/forBusinessGateway/1 \\");
        System.out.println("  -H \"Content-Type: application/json\" \\");
        System.out.println("  -H \"huditCompressed: 1\" \\");
        System.out.println("  -H \"Accept-Encoding: gzip\" \\");
        System.out.println("  --data-raw \"$(echo '" + base64Encoded + "' | base64 -d)\"");
        
        System.out.println("\n=== 或者使用文件方式 ===");
        System.out.println("# 1. 创建压缩数据文件");
        System.out.println("echo '" + base64Encoded + "' | base64 -d > compressed_data.bin");
        System.out.println("# 2. 发送请求");
        System.out.println("curl -X POST http://localhost:9000/dev-test/forBusinessGateway/1 \\");
        System.out.println("  -H \"Content-Type: application/json\" \\");
        System.out.println("  -H \"huditCompressed: 1\" \\");
        System.out.println("  -H \"Accept-Encoding: gzip\" \\");
        System.out.println("  --data-binary @compressed_data.bin");
    }

    /**
     * 使用RestTemplate发送压缩请求的示例
     */
    @Test
    public void sendCompressedRequest() {
        String originalJson = """
            {
                "merCode": "TEST001",
                "userId": "user123", 
                "action": "test",
                "timestamp": "2025-06-03T15:30:00"
            }
            """;

        try {
            // 1. 构建压缩请求体
            String compressedBody = buildCompressedBody(originalJson);
            
            // 2. 构建请求头
            HttpHeaders headers = buildCompressedHeaders();
            
            // 3. 创建请求实体
            HttpEntity<String> requestEntity = new HttpEntity<>(compressedBody, headers);
            
            // 4. 发送请求（注意：这里只是示例，实际运行需要网关服务启动）
            RestTemplate restTemplate = new RestTemplate();
            String url = "http://localhost:9000/dev-test/forBusinessGateway/1";
            
            System.out.println("\n=== 准备发送压缩请求 ===");
            System.out.println("URL: " + url);
            System.out.println("Headers: " + headers);
            System.out.println("Body size: " + compressedBody.getBytes().length + " bytes");
            
            // 注释掉实际请求，避免测试时连接失败
            // String response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class).getBody();
            // System.out.println("Response: " + response);
            
        } catch (Exception e) {
            System.out.println("请求发送失败（这在测试环境中是正常的）: " + e.getMessage());
        }
    }

    /**
     * 生成JMeter测试数据
     */
    @Test
    public void generateJMeterTestData() {
        String[] testJsons = {
            """
            {"merCode": "TEST001", "action": "login", "userId": "user001"}
            """,
            """
            {
                "merCode": "TEST002",
                "action": "order",
                "userId": "user002",
                "orderData": {
                    "items": [{"id": 1, "name": "商品A", "price": 100}],
                    "total": 100
                }
            }
            """,
            """
            {
                "merCode": "TEST003",
                "action": "query",
                "userId": "user003",
                "queryParams": {
                    "startDate": "2025-06-01",
                    "endDate": "2025-06-03",
                    "pageSize": 20,
                    "pageNum": 1
                }
            }
            """
        };

        System.out.println("\n=== JMeter测试数据 ===");
        for (int i = 0; i < testJsons.length; i++) {
            String compressedBody = buildCompressedBody(testJsons[i].trim());
            String base64Encoded = Base64.getEncoder().encodeToString(
                compressedBody.getBytes(StandardCharsets.ISO_8859_1)
            );
            
            System.out.println("测试数据 " + (i + 1) + ":");
            System.out.println("原始JSON: " + testJsons[i].trim());
            System.out.println("Base64编码的压缩数据: " + base64Encoded);
            System.out.println("---");
        }
    }

    public static void main(String[] args) {
        GZIPRequestBuilder builder = new GZIPRequestBuilder();
        
        System.out.println("=== GZIP请求构建器测试 ===\n");
        
        // 运行所有测试
        builder.testCompressionDecompression();
        builder.generateCurlCommand();
        builder.sendCompressedRequest();
        builder.generateJMeterTestData();
    }
}
