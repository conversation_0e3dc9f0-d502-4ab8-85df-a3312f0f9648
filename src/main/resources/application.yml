route-forbidden-config:
  urlList:
    - /actuator/shutdown
server:
  port: 9000

  #logging:
  #level:
  #org.springframework.cloud.gateway: debug

alarm:
  sendErrorLog:
    enable: false

logging:
  level:
    root: INFO

spring:
  codec:
    max-in-memory-size: 512KB
  config:
    use-legacy-processing: true
  application:
    name: businesses-gateway
  profiles:
    active: local
  main:
    allow-bean-definition-overriding: true
  cloud:
    discovery:
      client:
        health-indicator:
          enabled: false
    nacos:
      discovery:
        server-addr: http://10.4.3.210:8848;
        namespace: 63b6732e-80fc-49c2-ac83-4b09e119d48c
        metadata:
          department: NR
        register-enabled: false
    sentinel:
      filter:
        enabled: false
      scg:
        fallback:
          enabled: true
          ## response返回文字提示信息，
          mode: response
          response-status: 200
          response-body: '{"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}'
          content-type: application/json
      transport:
        dashboard: test-sentinel.hxyxt.com
      eager: true
      data-source:
        ## 配置流控规则，名字任意
        flow:
          nacos:
            ## nacos的地址
            server-addr: 10.4.3.210:8848
            namespace: 9a3c9b59-885b-4a31-87c0-0bdc33328a5d
            ## 配置ID
            dataId: ${spring.application.name}-gateway-flow-rules.json
            ## 配置分组，默认是DEFAULT_GROUP
            groupId: SENTINEL_GROUP
            ## 配置存储的格式
            data-type: json
            ## rule-type设置对应得规则类型，总共七大类型，在com.alibaba.cloud.sentinel.datasource.RuleType这个枚举类中有体现
            rule-type: gw-flow
        ## 配置流控规则，名字任意
        api:
          nacos:
            ## nacos的地址
            server-addr: 10.4.3.210:8848
            namespace: 9a3c9b59-885b-4a31-87c0-0bdc33328a5d
            ## 配置ID
            dataId: ${spring.application.name}-gateway-api-rules.json
            ## 配置分组，默认是DEFAULT_GROUP
            groupId: SENTINEL_GROUP
            ## 配置存储的格式
            data-type: json
            ## rule-type设置对应得规则类型，总共七大类型，在com.alibaba.cloud.sentinel.datasource.RuleType这个枚举类中有体现
            rule-type: gw-api-group
        ## 配置降级规则，名字任意
        degrade:
          nacos:
            ## nacos的地址
            server-addr: 10.4.3.210:8848
            namespace: 13949598-55d3-46ac-b0e8-42a9c30a1601
            ## 配置ID
            dataId: ${spring.application.name}-degrade-rules.json
            ## 配置分组，默认是DEFAULT_GROUP
            groupId: SENTINEL_GROUP
            ## 配置存储的格式
            data-type: json
            ## rule-type设置对应得规则类型，总共七大类型，在com.alibaba.cloud.sentinel.datasource.RuleType这个枚举类中有体现
            rule-type: degrade
    inetutils:
      preferred-networks:
        - ^10\.200.+
    gateway:
      httpclient:
        connect-timeout: 2000
        response-timeout: 20s
        pool:
          max-idle-time: PT10S
          eviction-interval: PT30S
      globalcors:
        corsConfigurations:
          '[/**]':
            allowedOriginPatterns: "*"
            #            allowedOrigins: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true
      routes:
        - id: order-service-forBusinessGateway
          uri: http://**********:8080
          predicates:
            - Path=/dev-test/**
          filters:
            - StripPrefix=1
        # develop-assistant
        - id: develop-assistant
          uri: http://**********:8088
          predicates:
            - Path=/develop-assistant/**
          filters:
            - StripPrefix=1
        # 订单对账服务
        - id: order-reconciliation
          uri: lb://order-reconciliation
          predicates:
            - Path=/order-reconciliation/**
          filters:
            - StripPrefix=1
        # 支付中台
        - id: yxt-payment
          uri: lb://yxt-payment
          predicates:
            - Path=/yxt-payment/**
          filters:
            - StripPrefix=1
        # 商户中台
        - id: hydee-middle-baseinfo
          uri: lb://hydee-middle-baseinfo
          predicates:
            - Path=/baseinfo/**
          filters:
            - StripPrefix=1
        # 一心助手中台服务跳转任务
        - id: assist-middle-portal-task
          uri: lb://assist-middle-portal
          predicates:
            - Path=/assist-middle-portal/c/article/**,/assist-middle-portal/c/formEvent/**,/assist-middle-portal/c/stats/**,/assist-middle-portal/c/task/**,/assist-middle-portal/c/taskItem/**
          filters:
            - StripPrefix=1
        # 商户平台
        - id: ydjia-merchant-platform
          uri: lb://ydjia-merchant-platform
          predicates:
            - Path=/merchant/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        - id: ydjia-operate
          uri: lb://ydjia-operate
          predicates:
            - Path=/operate/**
          filters:
            - StripPrefix=1
        # 药店加小前台
        - id: ydjia-merchant-manager
          uri: lb://ydjia-merchant-manager
          predicates:
            - Path=/mer-manager/**
          filters:
            - StripPrefix=1
        # 药学服务
        - id: ydjia-drug
          uri: lb://ydjia-drug
          predicates:
            - Path=/drug/**
          filters:
            - StripPrefix=1
        # 电商云O2O
        - id: hydee-business-order
          uri: lb://hydee-business-order
          predicates:
            - Path=/dscloud/**
          filters:
            - StripPrefix=1
        # 电商云B2C
        - id: hydee-business-order-web
          uri: lb://hydee-business-order-web
          predicates:
            - Path=/b2c/**
          filters:
            - StripPrefix=1
        # 会员中台
        - id: hydee-middle-member
          uri: lb://hydee-middle-member
          predicates:
            - Path=/member/**
          filters:
            - StripPrefix=1
        # 回访中台
        - id: yxt-middle-visit
          uri: lb://yxt-middle-visit
          predicates:
            - Path=/visit/**
          filters:
            - StripPrefix=1
        # 商品中台
        - id: hydee-middle-merchandise
          uri: lb://hydee-middle-merchandise
          predicates:
            - Path=/merchandise/**
          filters:
            - StripPrefix=1
        # 商品搜索
        - id: yxt-merchandise-search
          uri: lb://yxt-merchandise-search
          predicates:
            - Path=/merchandiseSearch/**
          filters:
            - StripPrefix=1
        # 微商城商品服务
        - id: hydee-middle-goods
          uri: lb://hydee-middle-goods
          predicates:
            - Path=/goods/**
          filters:
            - StripPrefix=1
        # 促销小前台
        - id: ydjia-merchant-promote
          uri: lb://ydjia-merchant-promote
          predicates:
            - Path=/promote/**
          filters:
            - StripPrefix=1
        # C端小前台
        - id: ydjia-merchant-customer
          uri: lb://ydjia-merchant-customer
          predicates:
            - Path=/customer/**
          filters:
            - StripPrefix=1
        # 支付中台
        - id: hydee-middle-payment
          uri: lb://hydee-middle-payment
          predicates:
            - Path=/payment/**
          filters:
            - StripPrefix=1
        # 分销中台
        - id: hydee-middle-sdp
          uri: lb://hydee-middle-sdp
          predicates:
            - Path=/sdp/**
          filters:
            - StripPrefix=1
        # 小密桥数据
        - id: honey-bridge-data
          uri: lb://honey-bridge-data
          predicates:
            - Path=/honey-bridge-data/**
          filters:
            - StripPrefix=1
        # 小密医疗
        - id: honey-medical
          uri: lb://honey-medical
          predicates:
            - Path=/honey-medical/**
          filters:
            - StripPrefix=1
        # 小密ERP服务
        - id: honey-erp-service
          uri: lb://honey-erp-service
          predicates:
            - Path=/honey-erp-service/**
          filters:
            - StripPrefix=1
        # 小密activity
        - id: honey-activity
          uri: lb://honey-activity
          predicates:
            - Path=/honey-activity/**
          filters:
            - StripPrefix=1
        # 小密推送服务
        - id: hydee-middle-push
          uri: lb://hydee-middle-push
          predicates:
            - Path=/hydee-middle-push/**
          filters:
            - StripPrefix=1
        # 药店加统计服务
        - id: ydjia-statistic
          uri: lb://ydjia-statistic
          predicates:
            - Path=/ydjia-statistic/**
          filters:
            - StripPrefix=1
        # 口罩预约
        - id: mask-server
          uri: lb://mask-server
          predicates:
            - Path=/mask/**
          filters:
            - StripPrefix=1
        # 迁移服务
        - id: migrate
          uri: lb://migrate
          predicates:
            - Path=/migrate/**
          filters:
            - StripPrefix=1
          # 企业微信服务
        - id: hydee-ewx-service
          uri: lb://hydee-ewx-service
          predicates:
            - Path=/hydee-ewx-service/**
          filters:
            - StripPrefix=1
        # 企业微信小蜜服务
        - id: hydee-ewx-honey
          uri: lb://hydee-ewx-honey
          predicates:
            - Path=/hydee-ewx-honey/**
          filters:
            - StripPrefix=1
        # 微信直播
        - id: hydee-live-service
          uri: lb://hydee-live-service
          predicates:
            - Path=/hydee-live-service/**
          filters:
            - StripPrefix=1
        # 会员中台
        - id: hydee-middle-member
          uri: lb://hydee-middle-member
          predicates:
            - Path=/member/**
          filters:
            - StripPrefix=1
        # 微商城OMS对接服务
        - id: middle-datasync-message
          uri: lb://middle-datasync-message
          predicates:
            - Path=/datasync/**
          filters:
            - StripPrefix=1
        # 微商城订单中台
        - id: hydee-middle-order
          uri: lb://hydee-middle-order
          predicates:
            - Path=/middleorder/**
          filters:
            - StripPrefix=1
        # 库存全量同步走定向服务器
        - id: merchandise-sync
          uri: lb://hydee-middle-merchandise-sync
          predicates:
            - Path=/merchandise/1.0/sync/**
          filters:
            - StripPrefix=1
            # 直播中台
        - id: hydee-live-service
          uri: lb://hydee-live-service
          predicates:
            - Path=/live/**
          filters:
            - StripPrefix=1
            # 服务商小前台
        - id: hydee-sp-platform
          uri: lb://hydee-sp-platform
          predicates:
            - Path=/sp-platform/**
          filters:
            - StripPrefix=1
        # 报表服务
        - id: ydjia-report
          uri: lb://ydjia-report
          predicates:
            - Path=/ydjia-report/**
          filters:
            - StripPrefix=1
        # 供应商商品模块
        - id: ydjia-srm-goods
          uri: lb://ydjia-srm-goods
          predicates:
            - Path=/srm-goods/**
          filters:
            - StripPrefix=1
        # 供应商发货模块
        - id: ydjia-srm-delivery
          uri: lb://ydjia-srm-delivery
          predicates:
            - Path=/srm-delivery/**
          filters:
            - StripPrefix=1
        # 供应商账单模块
        - id: ydjia-srm-bills
          uri: lb://ydjia-srm-bills
          predicates:
            - Path=/srm-bills/**
          filters:
            - StripPrefix=1
        # 商户定制化模块
        - id: ydjia-adaptation
          uri: lb://ydjia-adaptation
          predicates:
            - Path=/ydjia-adaptation/**
          filters:
            - StripPrefix=1
        # 商品中台数据处理(erp/三方数据)
        - id: hydee-merchandise-data-processor
          uri: lb://hydee-merchandise-data-processor
          predicates:
            - Path=/data-processor/**
          filters:
            - StripPrefix=1
        # ERP数据同步
        - id: hydee-middle-syncerp
          uri: lb://hydee-middle-syncerp
          predicates:
            - Path=/syncerp/**
          filters:
            - StripPrefix=1
        # 预警中台
        - id: hydee-middle-alerting
          uri: lb://hydee-middle-alerting
          predicates:
            - Path=/hydee-middle-alerting/**
          filters:
            - StripPrefix=1
        - id: hydee-middle-market
          uri: lb://hydee-middle-market
          predicates:
            - Path=/market/**
          filters:
            - StripPrefix=1
        # promotion服务
        - id: yxt-middle-promotion
          uri: lb://yxt-middle-promotion
          predicates:
            - Path=/promotion/**
          filters:
            - StripPrefix=1
        # 账户中心
        - id: hydee-middle-account-center
          uri: lb://hydee-middle-account-center
          predicates:
            - Path=/account-center/**
          filters:
            - StripPrefix=1
        - id: hydee-es-sync
          uri: lb://hydee-es-sync
          predicates:
            - Path=/es-sync/**
          filters:
            - StripPrefix=1
        # 同步服务
        - id: hydee-middle-data-sync
          uri: lb://hydee-middle-data-sync
          predicates:
            - Path=/data-sync/**
          filters:
            - StripPrefix=1
        # 随心看
        - id: hydee-data-center-follow-heart
          uri: lb://hydee-data-center-business
          predicates:
            - Path=/hydee-data-center-business/**
          filters:
            - StripPrefix=1
        # 第三方微信平台
        - id: hydee-middle-third
          uri: lb://hydee-middle-third
          predicates:
            - Path=/middle-third/**
          filters:
            - StripPrefix=1
        # 营销中台
        - id: hydee-middle-market
          uri: lb://hydee-middle-market
          predicates:
            - Path=/market/**
          filters:
            - StripPrefix=1
        #临时测试支付接口
        - id: h3-pay-core
          uri: lb://h3-pay-core
          predicates:
            - Path=/paycore/**
          filters:
            - StripPrefix=1
        # 一心助手任务中台
        - id: assist-task
          uri: lb://assist-task
          predicates:
            - Path=/assist-task/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手综合服务-门店申请相关
        - id: assist-synthesis-store-order
          uri: lb://assist-core-toolkit
          predicates:
            - Path=/assist-synthesis/c/storeOrder/**,/assist-synthesis/c/storeOrderBizConfig/**,/assist-synthesis/c/storeReplenish/**,/assist-synthesis/c/bizCommodity/**,/assist-synthesis/b/storeOrder/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手成长中心
        - id: assist-growth
          uri: lb://assist-growth
          predicates:
            - Path=/assist-growth/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手中台门面
        - id: assist-middle-portal
          uri: lb://assist-middle-portal
          predicates:
            - Path=/assist-middle-portal/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 前台消息
        - id: assist-middle-portal-message
          uri: lb://assist-middle-portal
          predicates:
            - Path=/assist-middle-portal/c/message/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 前台用户设备信息
        - id: assist-middle-portal-message
          uri: lb://assist-middle-portal
          predicates:
            - Path=/assist-middle-portal/c/userdevice/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手首页应用
        - id: assist
          uri: lb://assist-home
          predicates:
            - Path=/assist-home/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手综合服务
        - id: assist-synthesis
          uri: lb://assist-synthesis
          predicates:
            - Path=/assist-synthesis/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手工具服务
        - id: assist-core-toolkit
          uri: lb://assist-core-toolkit
          predicates:
            - Path=/assist-core-toolkit/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手统计
        - id: assist-stats
          uri: lb://assist-stats
          predicates:
            - Path=/assist-stats/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手资源服务
        - id: assist-resource
          uri: lb://assist-resource
          predicates:
            - Path=/assist-resource/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 流程引擎
        - id: yxt-workflow
          uri: lb://yxt-workflow
          predicates:
            - Path=/yxt-workflow/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        #APP消息平台
        - id: yxt-app-push
          uri: lb://yxt-app-push
          predicates:
            - Path=/yxt-app-push/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        #公告导出服务
        - id: yxt-export
          uri: lb://yxt-export
          predicates:
            - Path=/yxt-export/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手小前台
        - id: assist-prospect
          uri: lb://assist-prospect
          predicates:
            - Path=/assist-prospect/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心基础组件服务
        - id: yxt-basis
          uri: lb://yxt-basis
          predicates:
            - Path=/yxt-basis/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心基础组件服务-new
        - id: assist-basis
          uri: lb://assist-basis
          predicates:
            - Path=/assist-basis/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心基础组件服务-new
        - id: assist-forum
          uri: lb://assist-forum
          predicates:
            - Path=/assist-forum/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 大数据心镜
        - id: yxt-bigdata-decision
          uri: lb://yxt-bigdata-decision
          predicates:
            - Path=/decision/**
          filters:
            - StripPrefix=1
          metadata: # 路由级别的超时配置，优先级大于网关的全局超时配置
            connect-timeout: 60000
            response-timeout: 60000
        - id: yxt-medical-prescription
          uri: lb://yxt-medical-prescription
          predicates:
            - Path=/yxt-medical-prescription/**
          filters:
            - StripPrefix=1
        # 大数据bee
        - id: yxt-bigdata-bee
          uri: lb://yxt-bigdata-bee
          predicates:
            - Path=/bigdata-bee/**
          filters:
            - StripPrefix=1
        # 大数据alarm
        - id: yxt-bigdata-alarm
          uri: lb://yxt-bigdata-alarm
          predicates:
            - Path=/bigdata-alarm/**
          filters:
            - StripPrefix=1
        # 一心助手HCM
        - id: assist-hcm
          uri: lb://assist-hcm
          predicates:
            - Path=/assist-hcm/**
          filters:
            - StripPrefix=1
            # ==走新网关认证===
            - YxtLoginAccess
        # 虚拟服务
        - id: yxt-mock-server
          uri: lb://yxt-mock-server
          predicates:
            - Path=/mock/**
          filters:
            - StripPrefix=1
        # 大数据心智
        - id: yxt-bigdata-mind
          uri: lb://yxt-bigdata-mind
          predicates:
            - Path=/mind/**
          filters:
            - StripPrefix=1
          metadata: # 路由级别的超时配置，优先级大于网关的全局超时配置
            connect-timeout: 60000
            response-timeout: 60000
        # 一心堂小前台
        - id: yxt-org-aspect
          uri: lb://yxt-basis-prospect
          predicates:
            - Path=/yxt-org-aspect/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心助手B2B加盟商城
        - id: yxt-mall-b2b
          uri: lb://yxt-mall-b2b
          predicates:
            - Path=/yxt-mall-b2b/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # yxt-login
        
        #=====登录路由控制=======

        #应急策略，如果yxt-login不可用，切换到ydjia-merchant-platform
        # - id: yxt-login-login-route-emergency
        #   uri: lb://ydjia-merchant-platform
        #   predicates:
        #     - Path=/yxt-login/c/token/w/1.0/login
        #   filters:
        #     - StripPrefix=1
        #     - RewritePath=/c/token/w/1.0/login, /1.0/acc/_login
        #=====登录路由控制end=======
        # 新服务路由
        - id: yxt-login
          uri: lb://yxt-login
          predicates:
            - Path=/yxt-login/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 客服中台
        - id: customer-service-center
          uri: lb://customer-service-center
          predicates:
            - Path=/customer-service-center/**
          filters:
            - StripPrefix=1
        # 一心堂基础前台服务
        - id: yxt-basis-prospect
          uri: lb://yxt-basis-prospect
          predicates:
            - Path=/yxt-basis-prospect/**
          filters:
            - StripPrefix=1
        # 订单新模型
        - id: order-service
          uri: lb://order-service
          predicates:
            - Path=/order-world/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 订单批处理服务
        - id: order-batch-service
          uri: lb://order-batch-service
          predicates:
            - Path=/order-batch/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 一心堂安全中心
        - id: yxt-safe-center
          uri: lb://yxt-safe-center
          predicates:
            - Path=/safe-center/**
          filters:
            - StripPrefix=1
            - SafeCenterAuth
            - YxtLoginAccess
        # 大数据bear
        - id: yxt-bigdata-bear
          uri: lb://yxt-bigdata-bear
          predicates:
            - Path=/bigdata-bear/**
          filters:
            - StripPrefix=1
        # 评价中台
        - id: evaluation-center
          uri: lb://evaluation-center
          predicates:
            - Path=/evaluation-center/**
          filters:
            - StripPrefix=1
        # 采购中台
        - id: purchase-order-center
          uri: lb://purchase-order-center
          predicates:
            - Path=/purchase-center/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 会员、慢病数据开发服务
        - id: yxt-bigdata-deer
          uri: lb://yxt-bigdata-deer
          predicates:
            - Path=/deer/**
          filters:
            - StripPrefix=1
          metadata: # 路由级别的超时配置，优先级大于网关的全局超时配置
            connect-timeout: 60000
            response-timeout: 60000
        # 大数据自助取数
        - id: yxt-bigdata-rabbit
          uri: lb://yxt-bigdata-rabbit
          predicates:
            - Path=/bigdata-rabbit/**
          filters:
            - StripPrefix=1
        # 大数据新智能请货
        - id: yxt-bigdata-turtle
          uri: lb://yxt-bigdata-turtle
          predicates:
            - Path=/bigdata-turtle/**
          filters:
            - StripPrefix=1
        # 价格中台
        - id: yxt-lot-price
          uri: lb://yxt-lot-price
          predicates:
            - Path=/lot-price/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
  redis:
    password: tThnBkJCgX
    jedis:
      pool:
        min-idle: 10
        max-active: 200
        max-idle: 50
        max-wait: 1000
    timeout: 1000
    cluster:
      nodes: **********:7000,**********:7001,**********:7000,**********:7001,**********:7000,**********:7001
      max-redirects: 3
api:
  base-info-version: 1.0

management:
  endpoint:
    mappings:
      enabled: true
    httptrace:
      enabled: true
    metrics:
      enabled: true
    health:
      enabled: true
      show-details: always
  health:
    defaults:
      enabled: false
  endpoints:
    web:
      exposure:
        include: ["*"]

feign:
  okhttp:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 30000
        readTimeout: 60000
        loggerLevel: full

rest:
  connectTimeout: 3000
  readTimeout: 6000
jwt:
  expire: 14400
  rsa-secret: xx1WET12^%3^(WE45
auth:
  user:
    token-header: Authorization

idempotent:
  expire-mill: 2000
  post-path:
    - /merchant/1.0/org
    - /merchant/1.0/authority
    - /merchant/1.0/employee
    - /operate/1.0/package
    - /operate/1.0/merchant
    - /operate/1.0/merchant/_purchasePkg
    - /operate/1.0/authority
    - /operate/1.0/authority/_createAcc
    - /mer-manager/1.0/pageset
    - /mer-manager/1.0/commodity
    - /mer-manager/1.0/commodity/self
    - /mer-manager/1.0/comm-dimen/add
    - /mer-manager/1.0/comm-type/addType
    - /mer-manager/1.0/assemble-comm
    - /promote/1.0/admin/activities
    - /mer-manager/1.0/csd-msg
    - /mask/1.0/b/store/_save
    - /mask/1.0/b/product/_save
    - /hydee-ewx-honey/1.0/clientStoreInspectTaskController/firstSubmit
    - /c/common/config/r/1.0/listGatewayRouteRule
gate:
  ignore:
    start-with:
      - /datasync/
      - /promote/
      - /open
      - /customer/
      - /ydjia-statistic/
      - /payment/1.0/receive/notify
      - /payment/1.0/receive/return
      - /payment/payment/payNotify
      - /payment/payment/refundNotify
      - /payment/payment/sharingNotify
      - /payment/1.0/unifiedPay/payNotify
      - /payment/1.0/unifiedPay/refundNotify
      - /payment/account/settleNotify
      - /merchant/1.0/acc/_queryAccountStatus/
      - /merchant/1.0/verification/
      - /merchant/1.0/acc/_activateAccount
      - /merchant/1.0/acc/getSelfBuildAppLoginUrl
      - /merchant/1.0/acc/check_user
      - /merchant/1.0/acc/checkShopStaff
      - /merchant/1.0/acc/update/avatar
      - /merchant/1.0/file/_upload
      - /mer-manager/1.0/express-record
      - /mer-manager/1.0/weeChatOpen/auth
      - /mer-manager/1.0/weeChatOpen/recvMsg/
      - /middle-third/1.0/open/receiveTicket
      - /middle-third/1.0/open/recvMsg/
      - /middle-third/1.0/open/authCallBack
      - /honey-medical/v1/mobileUser/getToken
      - /honey-medical/v1/monitor/healthCheck
      - /honey-medical/v1/scanLogin/getScanElement
      - /honey-medical/v1/scanLogin/isAppLoginCompleted
      - /honey-medical/v1/sso/getSSOToken
      - /merchandise/1.0/store-spec/sync
      - /merchandise/1.0/ds/op/file
      - /merchandise/1.0/sync/stock
      - /merchandise/1.0/sync/price
      - /merchandise/1.0/sync/commAndStore
      - /merchandise/1.0/open
      - /merchandise/1.0/comm-spec/access-plat
      - /mask/1.0/c
      - /dscloud/1.0/ds/print
      - /dscloud/1.0/ds/file
      - /dscloud/1.0/ds/order/callback/orderDelivery
      - /hydee-ewx-service/1.0/honeyApp/dataCallback
      - /hydee-ewx-service/1.0/honeyApp/orderCallback
      - /hydee-ewx-service/1.0/innerHoneyApp/contactCallback
      - /hydee-ewx-service/1.0/innerHoneyApp/customerCallback
      - /hydee-ewx-service/1.0/customHoneyApp/callback
      - /hydee-ewx-honey/1.0/third/plat/callBack
      - /hydee-ewx-honey/1.0/third/plat/authCallBack
      - /hydee-ewx-honey/1.0/third/plat/recvMsg
      - /hydee-ewx-honey/1.0/ewxService/getMiniInfo
      - /hydee-ewx-honey/1.0/wxclient
      - /hydee-ewx-honey/1.0/boss
      - /hydee-ewx-honey/1.0/login/fromAdmin
      - /hydee-ewx-honey/1.0/login/fromMobile
      - /hydee-ewx-honey/1.0/login/fromInner
      - /hydee-ewx-honey-py/1.0/login/fromAdmin
      - /hydee-ewx-honey-py/1.0/login/fromMobile
      - /hydee-live-service/1.0/videoCallback/recordVideoCallback
      - /hydee-live-service/1.0/videoCallback/pushVideoCallback
      - /member/1.0/memberManage/changeMemberCard
      - /member/1.0/erpMember/incrSyncMember
      - /member/1.0/memberCrowd/getExpertsCrowdInfo
      - /member/1.0/memberCrowd/experts/delete
      - /market/1.0/couponGift/order/callback
      - /mer-manager/1.0/oss/file/upload
      - /mer-manager/1.0/merchantSwitch/batchUpdate
      - /syncerp/1.0.0/pushCompany
      - /syncerp/1.0.0/pushStore
      - /syncerp/1.0.0/pushEmployee
      - /merchant/1.0/cjksso/getToken
      - /merchant/1.0/cjksso/_login
      - /sp-platform/1.0/spAcc/_login
      - /sp-platform/1.0/spAcc/_register
      - /sp-platform/1.0/spAcc/notLoginCheck
      - /sp-platform/1.0/spAcc/_sendCode
      - /sp-platform/1.0/spAcc/_checkVerificationCode
      - /sp-platform/1.0/file/_upload
      - /sp-platform/1.0/spAcc/forgetPassword
      - /ydjia-report/1.0/admin
      - /ydjia-report/vs
      - /ydjia-adaptation
      - /drug/
      - /hydee-middle-alerting/1.0/admin
      - /hydee-middle-alerting/vs
      - /b2c/1.0/oms/order/listByTime
      - /b2c/1.0/oms/order/listRefundByTime
      - /account-center/him/merchant/getList
      - /account-center/him/provider/getList
      - /market/1.0/ispActivity4Him/getDataByMerchant
      - /market/1.0/ispActivity4Him/getDataByIsp
      - /merchant/1.0/acc/_forgetPassword
      - /sp-platform/1.0/spAcc/search/
      - /merchant/1.0/acc/account/query/
      - /assist-synthesis/swagger
      - /assist-synthesis/v2/api-docs
      - /assist-synthesis/webjars
      - /assist-synthesis/swagger-resources
      - /assist-synthesis/doc.html
      - /assist-synthesis/druid
      - /assist-core-toolkit/swagger
      - /assist-core-toolkit/v2/api-docs
      - /assist-core-toolkit/webjars
      - /assist-core-toolkit/swagger-resources
      - /assist-core-toolkit/doc.html
      - /assist-core-toolkit/druid
      - /assist-growth/swagger
      - /assist-growth/v2/api-docs
      - /assist-growth/webjars
      - /assist-growth/swagger-resources
      - /assist-growth/druid
      - /assist-home/swagger
      - /assist-home/druid
      - /assist-home/v2/api-docs
      - /assist-home/webjars
      - /assist-home/swagger-resources
      - /assist-home/doc.html
      - /assist-task/swagger
      - /assist-task/v2/api-docs
      - /assist-task/webjars
      - /assist-task/swagger-resources
      - /assist-task/doc.html
      - /assist-task/druid
      - /assist-middle-portal/swagger
      - /assist-middle-portal/v2/api-docs
      - /assist-middle-portal/webjars
      - /assist-middle-portal/swagger-resources
      - /assist-middle-portal/doc.html
      - /assist-middle-portal/druid
      - /assist-middle-portal/c/common/weChat/r/1.0/getJsSdkAuthConfig
      - /assist-middle-portal/c/material/w/1.0/doSaveMaterialVisit
      - /yxt-basis/swagger
      - /yxt-basis/v2/api-docs
      - /yxt-basis/webjars
      - /yxt-basis/swagger-resources
      - /yxt-basis/doc.html
      - /yxt-basis/druid
      - /yxt-export/swagger
      - /yxt-export/v2/api-docs
      - /yxt-export/webjars
      - /yxt-export/druid
      - /yxt-export/swagger-resources
      - /yxt-export/doc.html
      - /yxt-app-push/swagger
      - /yxt-app-push/v2/api-docs
      - /yxt-app-push/webjars
      - /yxt-app-push/swagger-resources
      - /yxt-app-push/druid
      - /assist-synthesis/b/common/disposition/r/1.0/listPolling
      - /assist-synthesis/c/common/disposition/r/1.0/listPolling
      - /assist-synthesis/c/appVersion/r/1.0/queryLatestVersion
      - /assist-synthesis/b/appVersion/w/1.0/createAppVersionBySign
      - /decision/swagger
      - /decision/v2/api-docs
      - /decision/webjars
      - /decision/swagger-resources
      - /decision/doc.html
      - /assist-prospect/common/auth/r/1.0/checkAuthBeforeLogin
      - /assist-home/c/common/front/r/1.0/checkAuthBeforeLogin
      - /assist-home/c/common/front/r/2.0/checkAuthBeforeLogin
      - /yxt-medical-prescription/api/1.0/medical/staff/checkUploadSign
      - /yxt-medical-prescription/api/1.0/medical/staff/uploadSign
      - /assist-synthesis/b/appVersion/w/1.0/addAppVersionInfoAndResource
      - /assist-synthesis/b/appVersion/r/1.0/getLatestAppVersionResource
      - /assist-synthesis/b/appVersion/r/1.0/getLatestInstallPackage
      - /assist-synthesis/c/appVersion/r/1.0/getUpdateResource
      - /assist-synthesis/b/appVersion/r/1.0/getAppVersion
      - /assist-synthesis/b/appVersion/r/2.0/getLatestAppVersionResource
      - /bigdata-alarm/data/check/r/1.0/es/cargo/push
      - /bigdata-alarm/data/check/r/1.0/data/error/phone
      - /bigdata-alarm/data/check/r/1.0/data/receive/phone
      - /assist-hcm/swagger
      - /assist-hcm/v2/api-docs
      - /assist-hcm/webjars
      - /assist-hcm/swagger-resources
      - /assist-hcm/doc.html
      - /assist-hcm/druid
      - /mind/swagger
      - /mind/v2/api-docs
      - /mind/webjars
      - /mind/swagger-resources
      - /mind/doc.html
      - /mind/commission/msg/r/1.0/qw/receive
      - /yxt-login/swagger
      - /yxt-login/v2/api-docs
      - /yxt-login/webjars
      - /yxt-login/swagger-resources
      - /yxt-login/doc.html
      - /yxt-login/druid
      - /yxt-mall-b2b/swagger
      - /yxt-mall-b2b/v2/api-docs
      - /yxt-mall-b2b/webjars
      - /yxt-mall-b2b/swagger-resources
      - /yxt-mall-b2b/doc.html
      - /yxt-mall-b2b/druid
      - /yxt-basis-prospect/swagger
      - /yxt-basis-prospect/v2/api-docs
      - /yxt-basis-prospect/webjars
      - /yxt-basis-prospect/swagger-resources
      - /yxt-basis-prospect/doc.html
      - /yxt-basis-prospect/druid
      # 网关放过路由
      - /yxt-login/c/cas/w/1.0/authentication
      - /yxt-login/outer/cas/r/1.0/authentication
      #   - /yxt-login/b/account/w/1.0/modifyPassword
      - /assist-middle-portal/c/userdevice/w/1.0/submitAppHeartbeat
      - /assist-middle-portal/c/common/config/r/1.0/listGatewayRouteRule
      - /yxt-medical-prescription/third/1.0/lianOu/inquiryCallback
    end-with:
      - /_login
      - /login
      - /loginByPhone
      - /api-docs
      - /doc.html
      - /druid
      - /swagger-resources
      - /webjars
      - /swagger
    log-end:
      - /_login



token:
  resolver:
    #        igrone:
    #            mercodes: 999999,hydee,SPHYDEE
    body:
      enable: true

flowRules:
  '[
    {
        "resource":"ydjia-merchant-platform",
        "resourceMode":0,
        "count":10000,
        "intervalSec":1,
        "paramItem":{
            "parseStrategy":2,
            "fieldName":"merCode"
        }
    },
    {
        "resource":"hydee-business-order-web",
        "resourceMode":0,
        "count":10000,
        "intervalSec":1,
        "paramItem":{
            "parseStrategy":2,
            "fieldName":"merCode"
        }
    },
    
    {
        "resource":"data_sync_third_callback",
        "resourceMode":1,
        "count":2,
        "intervalSec":10
    }
  ]'
apiDefinitions:
  '[
    {
        "apiName":"data_sync_third_callback",
        "predicateItems":[
            {
                "pattern":"/data-sync/third/callback/27/selectStock/**",
                "matchStrategy":1
            }
        ]
    }
  ]'

grey:
  enable: true
  

# 动态路由功能开关
dynamic:
  enable: true

safe-center:
  gateway-channel: B
  auth:
    enable:
      list: yxt-safe-cente