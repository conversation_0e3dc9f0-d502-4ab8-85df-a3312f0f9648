package cn.hydee.gateway.filter;

import cn.hydee.gateway.config.ExcludeUrlConfig;
import cn.hydee.gateway.config.UserAuthConfig;
import cn.hydee.gateway.constants.CommonConstants;
import cn.hydee.gateway.domain.JWTInfo;
import cn.hydee.gateway.domain.ReturnData;
import cn.hydee.gateway.domain.TokenForbiddenResponse;
import cn.hydee.gateway.domain.UrlForbiddenResponse;
import cn.hydee.gateway.dto.TokenDTO;
import cn.hydee.gateway.filter.ReactiveYxtLoginAccessGatewayFilterFactory.Config;
import cn.hydee.gateway.util.Const;
import cn.hydee.gateway.util.UserAuthUtil;
import com.alibaba.fastjson.JSONObject;
import com.yxt.lang.constants.INumberValue;
import com.yxt.lang.util.JsonUtils;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import javax.annotation.Resource;

import com.yxt.safecenter.auth.sdk.constants.AuthConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.OrderedGatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.http.HttpCookie;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 响应式权限校验过滤器 - 优化版本
 * 
 * 主要优化点：
 * 1. 使用 ReactiveRedisTemplate 替代同步 Redis 操作
 * 2. 将多个 Redis 操作合并为响应式链式调用
 * 3. 避免阻塞 Reactor Netty 事件循环线程
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ReactiveYxtLoginAccessGatewayFilterFactory extends AbstractGatewayFilterFactory<Config> {

    private static final String URL_SPLIT = "/";
    private static final String PATH_VARIABLE_SPLIT = "/{";
    protected final static String HEAD_USER_ID_KEY = "userId";
    private final static String HEAD_USER_NAME_KEY = "userName";

    /**
     * 用户中文名
     */
    private final static String HEAD_USER_ZH_NAME_KEY = "userZhName";
    /**
     * 员工编码标识
     */
    public static final String HEAD_EMP_CODE = "empCode";

    /**
     * 是否要重新校验token
     */
    public static final String RE_CHECK_TOKEN = "reCheckToken";
    /**
     * 账号类型标识
     */
    public static final String HEAD_ACC_TYPE = "accType";
    /**
     * 商户编码标识
     */
    protected final static String HEAD_USER_MERCODE = "merCode";

    @Value("${spring.profiles.active}")
    private String env;

    @Resource
    private ExcludeUrlConfig excludeUrlConfig;

    @Resource
    private UserAuthConfig userAuthConfig;

    @Autowired
    private ReactiveStringRedisTemplate reactiveStringRedisTemplate;
    @Autowired
    private ReactiveRedisTemplate<String, Object> reactiveRedisTemplate;
    @Resource
    private UserAuthUtil userAuthUtil;

    @Value("${accessReleaseOnOff:false}")
    private Boolean accessReleaseOnOff;
    @Value("${hyDeeRequestDecoratorReleaseOnOff:false}")
    private Boolean hyDeeRequestDecoratorReleaseOnOff;
    //针对哪些商户放行
    @Value("${token.resolver.igrone.mercodes:'SPHYDEE,hydee'}")
    private String HEAD_PASS_MERCODES = "SPHYDEE";

    public ReactiveYxtLoginAccessGatewayFilterFactory() {
        super(Config.class);
    }

    /**
     * 指定拦截器顺序，在{@link cn.hydee.gateway.filter.AccessGatewayFilter}之前
     */
    @Override
    public GatewayFilter apply(Config config) {
        return new OrderedGatewayFilter((exchange, chain) -> {
            //如果不重新校验,则直接放行
            if (Boolean.FALSE.equals(exchange.getAttributes().get(AuthConstants.OLD_AUTH_FLAG))) {
                // 老的全局token鉴权过滤器也不鉴权
                exchange.getAttributes().put(RE_CHECK_TOKEN, Boolean.FALSE);
                return chain.filter(exchange);
            }
            ServerHttpRequest request = exchange.getRequest();
            String requestUri = request.getPath().pathWithinApplication().value();
            String method = request.getMethodValue();

            // 日志记录
            if (log.isDebugEnabled()) {
                log.debug("method:{} requestUri：{}", method, requestUri);
            }

            exchange.getAttributes().put(RE_CHECK_TOKEN, Boolean.TRUE);
            
            // 检查是否需要拦截
            if (excludeUrlConfig.isStartWith(requestUri) || excludeUrlConfig.isEndWith(requestUri)) {
                return permit(exchange, chain);
            }

            // 响应式处理用户认证和权限校验
            return processUserAuthentication(request, exchange, chain, method, requestUri)
                    .onErrorResume(e -> {
                        log.error("==== 执行ReactiveYxtLoginAccess失败====，method:{} requestUri：{}", method, requestUri, e);
                        return permit(exchange, chain);
                    });
        }, FilterOrder.YxtLoginAccessGatewayFilter);
    }

    /**
     * 响应式处理用户认证和权限校验
     */
    private Mono<Void> processUserAuthentication(ServerHttpRequest request, ServerWebExchange exchange, 
                                               GatewayFilterChain chain, String method, String requestUri) {
        ServerHttpRequest.Builder mutate = request.mutate();
        
        return getJWTInfoReactive(request, mutate)
                .switchIfEmpty(Mono.error(new RuntimeException("User Token Forbidden or Expired!")))
                .flatMap(user -> {
                    // 判断用户是否有效 - 响应式检查
                    return reactiveStringRedisTemplate.opsForSet()
                            .isMember(Const.EFFECTIVE_LOGIN_USER_ID, user.getUserId())
                            .flatMap(isValidUser -> {
                                if (!isValidUser) {
                                    return getVoidMono(exchange, new TokenForbiddenResponse("User Token Forbidden or Expired!"));
                                }
                                
                                // 不再进行网关基础校验
                                exchange.getAttributes().put(RE_CHECK_TOKEN, Boolean.FALSE);
                                
                                // 判断链接是否收费，不收费放行
                                return matchNeedPayPathReactive(method, requestUri)
                                        .flatMap(matchPath -> {
                                            if (StringUtils.isEmpty(matchPath)) {
                                                return permit(exchange, chain);
                                            }
                                            
                                            // 判断资源是否有效 - 响应式检查
                                            return checkUserPermissionReactive(user.getUserId(), matchPath)
                                                    .flatMap(hasPermission -> {
                                                        if (!hasPermission) {
                                                            return getVoidMono(exchange, new UrlForbiddenResponse("User Forbidden!Does not has Permission!"));
                                                        }
                                                        
                                                        ServerHttpRequest build = mutate.build();
                                                        return chain.filter(exchange.mutate().request(build).build());
                                                    });
                                        });
                            });
                })
                .onErrorResume(e -> {
                    if (e.getMessage().contains("Token Forbidden")) {
                        return getVoidMono(exchange, new TokenForbiddenResponse("User Token Forbidden or Expired!"));
                    }
                    return permit(exchange, chain);
                });
    }

    /**
     * 响应式获取JWT用户信息
     */
    private Mono<JWTInfo> getJWTInfoReactive(ServerHttpRequest request, ServerHttpRequest.Builder mutate) {
        // cookie中获取不到，则会从请求头中获取Token
        String token = getToken(request);
        // 不存在token直接返回null
        if (StringUtils.isBlank(token)) {
            return Mono.empty();
        }

        // 将token转换为解析对象
        String cookieRedisKey = Const.REDIS_BASE_PREFIX + this.hashToken(token);
        
        return reactiveStringRedisTemplate.opsForValue().get(cookieRedisKey)
                .cast(String.class)
                .map(tokenStr -> {
                    TokenDTO userDTO = JsonUtils.toObject(tokenStr, TokenDTO.class);
                    return this.convert2JWTInfo(userDTO);
                })
                .switchIfEmpty(Mono.fromCallable(() -> {
                    try {
                        return userAuthUtil.getInfoFromToken(token);
                    } catch (Exception e) {
                        return null;
                    }
                }))
                .filter(Objects::nonNull)
                .flatMap(user -> {
                    // 如果是多端登录检查
                    if (Objects.equals(user.getMultiLogin(), Boolean.FALSE.toString()) && StringUtils.isNotBlank(user.getUserId())) {
                        return reactiveStringRedisTemplate.opsForValue()
                                .get(Const.REDIS_OAUTH_LOGIN_TOKEN_KEY + user.getUserId())
                                .cast(String.class)
                                .filter(loginTokenCache -> StringUtils.equals(token, loginTokenCache))
                                .map(loginTokenCache -> user)
                                .switchIfEmpty(Mono.empty());
                    }
                    return Mono.just(user);
                })
                .doOnNext(user -> {
                    // 设置用户header参数
                    mutate.headers(httpHeaders -> {
                        httpHeaders.set(HEAD_USER_ID_KEY, user.getUserId());
                        httpHeaders.set(HEAD_USER_NAME_KEY, user.getUserName());
                        httpHeaders.set(HEAD_EMP_CODE, user.getEmpCode());
                        try {
                            httpHeaders.set(HEAD_USER_ZH_NAME_KEY, URLEncoder.encode(user.getZhName(), "utf-8"));
                        } catch (UnsupportedEncodingException e) {
                            log.error("Encoding error: ", e);
                            throw new RuntimeException(e);
                        }
                        httpHeaders.set(HEAD_ACC_TYPE, user.getAccType() == null ? "" : String.valueOf(user.getAccType()));
                    });

                    String headerMerCode = request.getHeaders().getFirst(HEAD_USER_MERCODE);
                    String tokenMerCodee = user.getMerCode();
                    log.debug("打印参数据{},{}", headerMerCode, tokenMerCodee);
                    
                    //登录强制重置header里面的商户号
                    if (!ObjectUtils.isEmpty(tokenMerCodee) && !HEAD_PASS_MERCODES.contains(tokenMerCodee)) {
                        List<String> merCodes = new ArrayList<>();
                        merCodes.add(tokenMerCodee);
                        mutate.headers(httpHeaders -> {
                            httpHeaders.put(HEAD_USER_MERCODE, merCodes);
                        });
                    }
                    
                    // 异步设置用户中文名到Redis
                    setZhNameToRedisReactive(user).subscribe();
                })
                .filter(user -> {
                    String headerMerCode = request.getHeaders().getFirst(HEAD_USER_MERCODE);
                    String tokenMerCodee = user.getMerCode();
                    
                    if (!ObjectUtils.isEmpty(headerMerCode) && !ObjectUtils.isEmpty(tokenMerCodee) && !headerMerCode.equals(tokenMerCodee)) {
                        if (!HEAD_PASS_MERCODES.contains(tokenMerCodee)) {
                            log.debug("商户token信息不匹配{},{}", headerMerCode, tokenMerCodee);
                            return false;
                        }
                    }
                    return true;
                });
    }

    /**
     * 响应式匹配当前资源是否是需要支付的资源
     */
    private Mono<String> matchNeedPayPathReactive(String method, String path) {
        return reactiveRedisTemplate.opsForSet().members(Const.NEED_PAY_RES_KEY)
                .cast(String.class)
                .filter(expect -> isRight(expect, method + path))
                .next()
                .defaultIfEmpty("");
    }

    /**
     * 响应式校验用户权限
     */
    private Mono<Boolean> checkUserPermissionReactive(String userId, String matchPath) {
        return reactiveRedisTemplate.opsForHash()
                .get(CommonConstants.REDIS_PERMISSION_KEY + userId, matchPath)
                .cast(Date.class)
                .map(date -> System.currentTimeMillis() <= date.getTime())
                .defaultIfEmpty(false);
    }

    /**
     * 响应式设置用户中文名到Redis
     */
    private Mono<Boolean> setZhNameToRedisReactive(JWTInfo info) {
        return reactiveStringRedisTemplate.opsForHash()
                .put(Const.USER_ZH_NAME_KEY, info.getUserName(), info.getZhName());
    }

    private JWTInfo convert2JWTInfo(TokenDTO userDTO) {
        JWTInfo jwtInfo = new JWTInfo();
        jwtInfo.setUserName(userDTO.getUserName());
        jwtInfo.setZhName(userDTO.getZhName());
        jwtInfo.setMerCode(userDTO.getMerCode());
        jwtInfo.setUserId(userDTO.getUserId());
        jwtInfo.setExprie(userDTO.getExpire());
        jwtInfo.setEmpCode(userDTO.getEmpCode());
        jwtInfo.setAccType(Optional.ofNullable(userDTO.getAccType()).map(INumberValue::value).orElse(null));
        jwtInfo.setMultiLogin(String.valueOf(userDTO.getMultiLogin()));
        return jwtInfo;
    }

    private String getToken(ServerHttpRequest request) {
        MultiValueMap<String, HttpCookie> cookies = request.getCookies();
        for (Entry<String, List<HttpCookie>> entry : cookies.entrySet()) {
            String cookieName = entry.getKey();
            //从cookie中获取当前环境的token
            if ((env + "_token").equals(cookieName) && CollectionUtils.isNotEmpty(entry.getValue())) {
                return Optional.ofNullable(entry.getValue().get(0)).map(HttpCookie::getValue).orElse(null);
            }
        }
        //cookie中获取不到，则采用老的方式从header中获取
        String authToken = request.getHeaders().getFirst(userAuthConfig.getTokenHeader());
        if (StringUtils.isBlank(authToken)) {
            authToken = request.getQueryParams().getFirst("token");
        }
        return authToken;
    }

    private Mono<Void> permit(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String requestUri = request.getPath().pathWithinApplication().value();
        if (excludeUrlConfig.isLogEndWith(requestUri) && Const.METHOD_POST.equals(request.getMethodValue())) {
            ServerHttpRequestDecorator decorator = new HyDeeRequestDecorator(request,
                hyDeeRequestDecoratorReleaseOnOff);
            return chain.filter(exchange.mutate().request(decorator).build());
        }
        return chain.filter(exchange.mutate().request(request).build());
    }

    public static class Config {
        // 配置类，可以添加自定义配置属性，后续拓展可能会用到
    }

    /**
     * 生成字符串的md5
     */
    public String hashToken(String str) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(str.getBytes());
            StringBuilder hexString = new StringBuilder();

            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 网关抛异常
     */
    private Mono<Void> getVoidMono(ServerWebExchange serverWebExchange, ReturnData body) {
        serverWebExchange.getResponse().setStatusCode(HttpStatus.FORBIDDEN);
        byte[] bytes = JSONObject.toJSONString(body).getBytes(StandardCharsets.UTF_8);
        DataBuffer buffer = serverWebExchange.getResponse().bufferFactory().wrap(bytes);
        try {
            return serverWebExchange.getResponse().writeWith(Flux.just(buffer));
        } finally {
            if (accessReleaseOnOff) {
                DataBufferUtils.release(buffer);
            }
        }
    }

    /**
     * 实际访问路径是否与期望路径匹配
     */
    private boolean isRight(String respect, String actual) {
        if (respect.contains(PATH_VARIABLE_SPLIT)) {
            return respect.split(URL_SPLIT).length == actual.split(URL_SPLIT).length && actual.startsWith(
                respect.substring(0, respect.indexOf(PATH_VARIABLE_SPLIT)));
        } else {
            return actual.equals(respect);
        }
    }
}
