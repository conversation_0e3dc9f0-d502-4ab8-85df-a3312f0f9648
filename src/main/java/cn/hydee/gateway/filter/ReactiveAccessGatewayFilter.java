package cn.hydee.gateway.filter;

import cn.hydee.gateway.config.ExcludeUrlConfig;
import cn.hydee.gateway.config.UserAuthConfig;
import cn.hydee.gateway.constants.CommonConstants;
import cn.hydee.gateway.domain.JWTInfo;
import cn.hydee.gateway.domain.ReturnData;
import cn.hydee.gateway.domain.TokenForbiddenResponse;
import cn.hydee.gateway.domain.UrlForbiddenResponse;
import cn.hydee.gateway.util.Const;
import cn.hydee.gateway.util.UserAuthUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.GATEWAY_ORIGINAL_REQUEST_URL_ATTR;

/**
 * 响应式权限校验过滤器 - 优化版本
 * 
 * 主要优化点：
 * 1. 使用 ReactiveRedisTemplate 替代同步 Redis 操作
 * 2. 将多个 Redis 操作合并为响应式链式调用
 * 3. 避免阻塞 Reactor Netty 事件循环线程
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(ExcludeUrlConfig.class)
public class ReactiveAccessGatewayFilter extends AbstractGatewayFilter {

    private static final String URL_SPLIT = "/";
    private static final String PATH_VARIABLE_SPLIT = "/{";
    protected final static String HEAD_USER_ID_KEY = "userId";
    private final static String HEAD_USER_NAME_KEY = "userName";
    /**
     * 用户中文名
     */
    private final static String HEAD_USER_ZH_NAME_KEY = "userZhName";
    protected final static String HEAD_USER_MERCODE = "merCode";
    /**
     * 员工编码标识
     */
    public static final String HEAD_EMP_CODE = "empCode";
    /**
     * 账号类型标识
     */
    public static final String HEAD_ACC_TYPE = "accType";
    private static final String GATE_WAY_PREFIX = "/api";
    //针对哪些商户放行
    @Value("${token.resolver.igrone.mercodes:'SPHYDEE,hydee'}")
    private String HEAD_PASS_MERCODES = "SPHYDEE";

    @Autowired
    private ExcludeUrlConfig excludeUrlConfig;

    @Autowired
    private UserAuthConfig userAuthConfig;

    @Autowired
    private UserAuthUtil userAuthUtil;

    @Autowired
    private ReactiveRedisTemplate<String, Object> reactiveRedisTemplate;

    @Autowired
    private ReactiveStringRedisTemplate reactiveStringRedisTemplate;

    @Value("${accessReleaseOnOff:false}")
    private Boolean accessReleaseOnOff;
    @Value("${hyDeeRequestDecoratorReleaseOnOff:false}")
    private Boolean hyDeeRequestDecoratorReleaseOnOff;

    @Override
    public Mono<Void> filter(ServerWebExchange serverWebExchange, GatewayFilterChain gatewayFilterChain) {
        //如果不重新校验,则直接放行
        if (Boolean.FALSE.equals(serverWebExchange.getAttributes().get(YxtLoginAccessGatewayFilterFactory.RE_CHECK_TOKEN))) {
            return gatewayFilterChain.filter(serverWebExchange);
        }
        serverWebExchange.getAttributes()
            .computeIfAbsent(GATEWAY_ORIGINAL_REQUEST_URL_ATTR, s -> new LinkedHashSet<>());
        LinkedHashSet requiredAttribute = serverWebExchange.getRequiredAttribute(GATEWAY_ORIGINAL_REQUEST_URL_ATTR);
        ServerHttpRequest request = serverWebExchange.getRequest();
        String requestUri = request.getPath().pathWithinApplication().value();
        String method = request.getMethodValue();
        if (log.isDebugEnabled()) {
            log.debug("method:{} requestUri：{}", method, requestUri);
        }
        Iterator<URI> iterator = requiredAttribute.iterator();
        while (iterator.hasNext()) {
            URI next = iterator.next();
            if (next.getPath().startsWith(GATE_WAY_PREFIX)) {
                requestUri = next.getPath().substring(GATE_WAY_PREFIX.length());
            }
        }
        
        // 不进行登录拦截的地址
        if (excludeUrlConfig.isStartWith(requestUri) || excludeUrlConfig.isEndWith(requestUri)) {
            return permit(serverWebExchange, gatewayFilterChain);
        }

        ServerHttpRequest.Builder mutate = request.mutate();
        
        // 响应式处理用户认证和权限校验
        return processUserAuthenticationReactive(request, mutate, serverWebExchange, gatewayFilterChain, method, requestUri);
    }

    /**
     * 响应式处理用户认证和权限校验
     */
    private Mono<Void> processUserAuthenticationReactive(ServerHttpRequest request, ServerHttpRequest.Builder mutate,
                                                        ServerWebExchange serverWebExchange, GatewayFilterChain gatewayFilterChain,
                                                        String method, String requestUri) {
        return getJWTUserReactive(request, mutate)
                .switchIfEmpty(Mono.error(new RuntimeException("User Token Forbidden or Expired!")))
                .flatMap(user -> {
                    // 判断用户是否有效 - 响应式检查
                    return reactiveStringRedisTemplate.opsForSet()
                            .isMember(Const.EFFECTIVE_LOGIN_USER_ID, user.getUserId())
                            .flatMap(isValidUser -> {
                                if (!isValidUser) {
                                    return getVoidMono(serverWebExchange, new TokenForbiddenResponse("User Token Forbidden or Expired!"));
                                }
                                
                                // 判断链接是否收费，不收费放行
                                return matchNeedPayPathReactive(method, requestUri)
                                        .flatMap(matchPath -> {
                                            if (StringUtils.isEmpty(matchPath)) {
                                                return permit(serverWebExchange, gatewayFilterChain);
                                            }
                                            
                                            // 判断资源是否有效 - 响应式检查
                                            return checkUserPermissionReactive(user.getUserId(), matchPath)
                                                    .flatMap(hasPermission -> {
                                                        if (!hasPermission) {
                                                            return getVoidMono(serverWebExchange, new UrlForbiddenResponse("User Forbidden!Does not has Permission!"));
                                                        }
                                                        
                                                        ServerHttpRequest build = mutate.build();
                                                        return gatewayFilterChain.filter(serverWebExchange.mutate().request(build).build());
                                                    });
                                        });
                            });
                })
                .onErrorResume(e -> {
                    if (e.getMessage().contains("Token Forbidden")) {
                        return getVoidMono(serverWebExchange, new TokenForbiddenResponse("User Token Forbidden or Expired!"));
                    }
                    log.warn("用户Token过期异常:{}", e.getMessage());
                    return getVoidMono(serverWebExchange, new TokenForbiddenResponse("User Token Forbidden or Expired!"));
                });
    }

    /**
     * 响应式校验用户权限
     */
    private Mono<Boolean> checkUserPermissionReactive(String userId, String matchPath) {
        return reactiveRedisTemplate.opsForHash()
                .get(CommonConstants.REDIS_PERMISSION_KEY + userId, matchPath)
                .cast(Date.class)
                .map(date -> System.currentTimeMillis() <= date.getTime())
                .defaultIfEmpty(false);
    }

    /**
     * 响应式匹配当前资源是否是需要支付的资源
     */
    private Mono<String> matchNeedPayPathReactive(String method, String path) {
        return reactiveRedisTemplate.opsForSet().members(Const.NEED_PAY_RES_KEY)
                .cast(String.class)
                .filter(expect -> isRight(expect, method + path))
                .next()
                .defaultIfEmpty("");
    }

    /**
     * 响应式获取JWT用户信息
     */
    private Mono<JWTInfo> getJWTUserReactive(ServerHttpRequest request, ServerHttpRequest.Builder ctx) {
        String authToken = request.getHeaders().getFirst(userAuthConfig.getTokenHeader());
        if (StringUtils.isBlank(authToken)) {
            authToken = request.getQueryParams().getFirst("token");
        }
        if (authToken == null) {
            return Mono.empty();
        }

        final String token = authToken;
        return Mono.fromCallable(() -> userAuthUtil.getInfoFromToken(token))
                .onErrorReturn(null)
                .filter(Objects::nonNull)
                .flatMap(info -> {
                    String multiLogin = info.getMultiLogin();
                    String userId = info.getUserId();
                    
                    // 判断用户是否多端登录，如果非多端登录，需要验证当前用户是否已经登录的缓存key是否和当前token一致
                    if (Objects.equals(multiLogin, Boolean.FALSE.toString())) {
                        return reactiveStringRedisTemplate.opsForValue()
                                .get(Const.REDIS_OAUTH_LOGIN_TOKEN_KEY + userId)
                                .cast(String.class)
                                .filter(loginTokenCache -> Objects.equals(token, loginTokenCache))
                                .map(loginTokenCache -> info)
                                .switchIfEmpty(Mono.empty());
                    }
                    return Mono.just(info);
                })
                .doOnNext(info -> {
                    // 设置用户header参数
                    ctx.headers(httpHeaders -> {
                        httpHeaders.set(HEAD_USER_ID_KEY, info.getUserId());
                        httpHeaders.set(HEAD_USER_NAME_KEY, info.getUserName());
                        httpHeaders.set(HEAD_EMP_CODE, info.getEmpCode());
                        try {
                            httpHeaders.set(HEAD_USER_ZH_NAME_KEY,
                                URLEncoder.encode(info.getZhName(), String.valueOf(StandardCharsets.UTF_8)));
                        } catch (UnsupportedEncodingException e) {
                            throw new RuntimeException(e);
                        }
                        httpHeaders.set(HEAD_ACC_TYPE, info.getAccType() == null ? "" : String.valueOf(info.getAccType()));
                    });
                    
                    String headerMerCode = request.getHeaders().getFirst(HEAD_USER_MERCODE);
                    String tokenMerCodee = info.getMerCode();
                    log.debug("打印参数据{},{}", headerMerCode, tokenMerCodee);
                    
                    //登录强制重置header里面的商户号
                    if (!ObjectUtils.isEmpty(tokenMerCodee) && !HEAD_PASS_MERCODES.contains(tokenMerCodee)) {
                        List<String> merCodes = new ArrayList<>();
                        merCodes.add(tokenMerCodee);
                        ctx.headers(httpHeaders -> {
                            httpHeaders.put(HEAD_USER_MERCODE, merCodes);
                        });
                    }
                    
                    // 异步设置用户中文名到Redis
                    setZhNameToRedisReactive(info).subscribe();
                })
                .filter(info -> {
                    String headerMerCode = request.getHeaders().getFirst(HEAD_USER_MERCODE);
                    String tokenMerCodee = info.getMerCode();
                    
                    if (!ObjectUtils.isEmpty(headerMerCode) && !ObjectUtils.isEmpty(tokenMerCodee) && !headerMerCode.equals(tokenMerCodee)) {
                        if (!HEAD_PASS_MERCODES.contains(tokenMerCodee)) {
                            log.debug("商户token信息不匹配{},{}", headerMerCode, tokenMerCodee);
                            return false;
                        }
                    }
                    return true;
                });
    }

    /**
     * 响应式设置用户中文名到Redis
     */
    private Mono<Boolean> setZhNameToRedisReactive(JWTInfo info) {
        return reactiveStringRedisTemplate.opsForHash()
                .put(Const.USER_ZH_NAME_KEY, info.getUserName(), info.getZhName());
    }

    private Mono<Void> permit(ServerWebExchange serverWebExchange, GatewayFilterChain gatewayFilterChain) {
        ServerHttpRequest request = serverWebExchange.getRequest();
        String requestUri = request.getPath().pathWithinApplication().value();
        if (excludeUrlConfig.isLogEndWith(requestUri) && Const.METHOD_POST.equals(request.getMethodValue())) {
            // 打印用户登录信息,从请求里获取Post请求体
            ServerHttpRequestDecorator decorator = new HyDeeRequestDecorator(request,
                hyDeeRequestDecoratorReleaseOnOff);
            return gatewayFilterChain.filter(serverWebExchange.mutate().request(decorator).build());
        }
        return gatewayFilterChain.filter(serverWebExchange.mutate().request(request).build());
    }

    @Override
    public int getOrder() {
        return FilterOrder.AccessGatewayFilter;
    }

    /**
     * 网关抛异常
     */
    private Mono<Void> getVoidMono(ServerWebExchange serverWebExchange, ReturnData body) {
        serverWebExchange.getResponse().setStatusCode(HttpStatus.FORBIDDEN);
        byte[] bytes = JSONObject.toJSONString(body).getBytes(StandardCharsets.UTF_8);
        DataBuffer buffer = serverWebExchange.getResponse().bufferFactory().wrap(bytes);
        try {
            return serverWebExchange.getResponse().writeWith(Flux.just(buffer));
        } finally {
            if (accessReleaseOnOff) {
                DataBufferUtils.release(buffer);
            }
        }
    }

    /**
     * 实际访问路径是否与期望路径匹配
     */
    private boolean isRight(String respect, String actual) {
        if (respect.contains(PATH_VARIABLE_SPLIT)) {
            return respect.split(URL_SPLIT).length == actual.split(URL_SPLIT).length && actual.startsWith(
                respect.substring(0, respect.indexOf(PATH_VARIABLE_SPLIT)));
        } else {
            return actual.equals(respect);
        }
    }
}
