package cn.hydee.gateway.filter;

import cn.hydee.gateway.config.IdempotentConfig;
import cn.hydee.gateway.constants.RestCodeConstants;
import cn.hydee.gateway.domain.ReturnData;
import cn.hydee.gateway.util.Const;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.time.Duration;

/**
 * 响应式防重提交filter - 优化版本
 * 
 * 主要优化点：
 * 1. 使用 ReactiveRedisTemplate 替代同步 Redis 操作
 * 2. 将 increment 和 expire 操作合并为响应式链式调用
 * 3. 避免阻塞 Reactor Netty 事件循环线程
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2020/2/18 13:50
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(IdempotentConfig.class)
public class ReactiveIdempotentFilter implements GlobalFilter, Ordered {

    protected final static String HEAD_USER_ID_KEY = "userId";

    @Autowired
    private IdempotentConfig idempotentConfig;

    @Autowired
    private ReactiveRedisTemplate<String, Object> reactiveRedisTemplate;

    @Value("${idempotentReleaseOnOff:false}")
    private Boolean idempotentReleaseOnOff;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String requestUri = request.getPath().pathWithinApplication().value();
        String method = request.getMethodValue();
        
        if (!idempotentConfig.isContainWith(method, requestUri)) {
            // 放行
            return chain.filter(exchange);
        }
        
        // 从header中找到userId;
        String userId = request.getHeaders().getFirst(HEAD_USER_ID_KEY);
        String key = incrementKey(userId, method, requestUri);
        
        // 响应式处理防重提交逻辑
        return reactiveRedisTemplate.opsForValue().increment(key)
                .flatMap(result -> {
                    if (result != null && result > 1) {
                        // 阻止重复提交
                        ReturnData data = new ReturnData();
                        data.setCode(RestCodeConstants.CODE_REPEAT);
                        data.setMsg(Const.CODE_REPEAT_MSG);
                        return forbiddenIdempotent(exchange, data);
                    }
                    
                    // 设置失效时间 - 响应式操作
                    log.debug("设置失效时间：{} ms", idempotentConfig.getExpireMill());
                    return reactiveRedisTemplate.expire(key, Duration.ofMillis(idempotentConfig.getExpireMill()))
                            .then(chain.filter(exchange));
                })
                .onErrorResume(e -> {
                    log.error("防重提交过滤器执行异常", e);
                    // 异常情况下放行，避免影响正常业务
                    return chain.filter(exchange);
                });
    }

    @Override
    public int getOrder() {
        return FilterOrder.IdempotentFilter;
    }

    private String incrementKey(String userId, String method, String url) {
        return userId + "-" + method + "-" + url;
    }

    /**
     * 网关抛异常
     * @param body 返回的数据
     */
    private Mono<Void> forbiddenIdempotent(ServerWebExchange serverWebExchange, ReturnData body) {
        serverWebExchange.getResponse().setStatusCode(HttpStatus.OK);
        byte[] bytes = JSONObject.toJSONString(body).getBytes(StandardCharsets.UTF_8);
        DataBuffer buffer = serverWebExchange.getResponse().bufferFactory().wrap(bytes);
        try {
            return serverWebExchange.getResponse().writeWith(Flux.just(buffer));
        } finally {
            if (idempotentReleaseOnOff) {
                DataBufferUtils.release(buffer);
            }
        }
    }
}
