package cn.hydee.gateway.filter;

import static cn.hydee.gateway.util.GZIPUtils.compress;

import cn.hydee.gateway.util.GZIPUtils;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import lombok.extern.slf4j.Slf4j;
import org.reactivestreams.Publisher;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.filter.factory.rewrite.CachedBodyOutputMessage;
import org.springframework.cloud.gateway.support.BodyInserterContext;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ReactiveHttpOutputMessage;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserter;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.server.HandlerStrategies;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 重写URL参数与Body中商户编码
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-30
 */
@Slf4j
@Component
//@Configuration
public class BodygzipFilter implements GlobalFilter, Ordered {
    protected static final String HUDITCOMPRESSED = "huditCompressed";

    @Value("${releaseDataBufferTwiceOnOff:false}")
    private Boolean releaseDataBufferTwiceOnOff;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();

        // 跳过非Post请求
        HttpMethod method = request.getMethod();
        if (method != HttpMethod.POST) {
            return chain.filter(exchange);
        }

        // 处理Body中请求内容
        if (request.getHeaders().get(HUDITCOMPRESSED) != null && request.getHeaders().get(HUDITCOMPRESSED).size() > 0) {
            String huditCompressed = request.getHeaders().get(HUDITCOMPRESSED).get(0);
            if(huditCompressed != null && huditCompressed.equals("1")){
                HttpHeaders headers = new HttpHeaders();
                headers.putAll(exchange.getRequest().getHeaders());
                headers.remove(HttpHeaders.CONTENT_LENGTH);
                CachedBodyOutputMessage outputMessage = new CachedBodyOutputMessage(exchange, headers);

                ServerRequest serverRequest = ServerRequest.create(exchange, HandlerStrategies.withDefaults().messageReaders());
                Mono<String> modifiedBody = serverRequest.bodyToMono(String.class).flatMap(o -> {
                    String body = gzipBody(o);
                    return Mono.just(body);
                });
                BodyInserter<Mono<String>, ReactiveHttpOutputMessage> bodyInserter = BodyInserters.fromPublisher(modifiedBody, String.class);

                //获取响应体
                ServerHttpResponse originalResponse = exchange.getResponse();
                DataBufferFactory bufferFactory = originalResponse.bufferFactory();
                ServerHttpResponseDecorator decoratedResponse = new ServerHttpResponseDecorator(originalResponse) {
                    @Override
                    public Mono<Void> writeWith(Publisher<? extends DataBuffer> body) {
                        if (body instanceof Flux) {
                            Flux<? extends DataBuffer> fluxBody = (Flux<? extends DataBuffer>) body;
                            return super.writeWith(fluxBody.buffer().map(dataBuffers -> {
                                DataBufferFactory dataBufferFactory = new DefaultDataBufferFactory();
                                DataBuffer join = dataBufferFactory.join(dataBuffers);
                                byte[] content = new byte[join.readableByteCount()];
                                join.read(content);
                                if (releaseDataBufferTwiceOnOff) {
                                    // 释放掉内存
                                    DataBufferUtils.release(join);    // 这是多释放的一次
                                }

                                String responseString = new String(content, Charset.forName("UTF-8"));
                                //判断该浏览器是否支持gzip解码，如果支持gzip解码，则进行压缩
                                String acceptEncoding = request.getHeaders().getFirst("huditCompressed");
                                if(acceptEncoding != null){
                                    assert acceptEncoding != null;
                                    //是否支持压缩
                                    if(acceptEncoding.equals("1")){
//                                //支持压缩
                                        try {
                                            content = compress(responseString);
                                            content = new String(content,StandardCharsets.ISO_8859_1).getBytes(StandardCharsets.UTF_8);
                                            originalResponse.getHeaders().put("huditCompressed",request.getHeaders().get("huditCompressed"));
                                            originalResponse.getHeaders().setContentLength(content.length);
                                        } catch (IOException e) {
                                            e.printStackTrace();
                                        }
                                    }else{
                                        originalResponse.getHeaders().setContentLength(responseString.getBytes().length);
                                    }
                                }
                                return bufferFactory.wrap(content);
                            }));
                        }
                        return super.writeWith(body);
                    }
                };
                return bodyInserter.insert(outputMessage, new BodyInserterContext())
                        .then(Mono.defer(() -> {
                            ServerHttpRequestDecorator decorator = new ServerHttpRequestDecorator(
                                    exchange.getRequest()) {
                                @Override
                                public HttpHeaders getHeaders() {
                                    long contentLength = headers.getContentLength();
                                    HttpHeaders httpHeaders = new HttpHeaders();
                                    httpHeaders.putAll(super.getHeaders());
                                    if (contentLength > 0) {
                                        httpHeaders.setContentLength(contentLength);
                                    } else {
                                        // TODO: this causes a 'HTTP/1.1 411 Length Required' on httpbin.org
                                        httpHeaders.set(HttpHeaders.TRANSFER_ENCODING, "chunked");
                                    }
                                    return httpHeaders;
                                }
                                @Override
                                public Flux<DataBuffer> getBody() {
                                    return outputMessage.getBody();
                                }
                            };
                            return chain.filter(exchange.mutate().request(decorator).build().mutate().response(decoratedResponse).build());
                        }));

//                        .then(Mono.defer(() -> {
//                            return chain.filter(exchange.mutate().response(decoratedResponse).build());
//                        }));
            }else{
                return chain.filter(exchange);
            }
        } else {
            return chain.filter(exchange);
        }
    }

    @Override
    public int getOrder() {
        return FilterOrder.BodygzipFilter;
    }

    /**
     * 替换Body中商户编码
     *
     * @param body 原报文
     * @return 修改后报文
     */
    private String gzipBody(String body) {
//        log.info("解压body之前：{}",body);
        //解压body逻辑
        try {
            Integer leold = body.getBytes().length;
            byte[] compress = compress(body);
            body = GZIPUtils.uncompressToString(compress);
//            log.info("解压body之后：{}",body);
            Integer lenew = body.getBytes().length;
            try{
//                log.info("body接收大小：{}，解压后大小：{}，压缩比例：{}",leold,lenew,(100-((leold*100)/lenew))+"%");
            }catch (Exception e){}
        }catch (Exception e){
//            log.info("解压包体报错：{}",e);
        }
        return body;
    }

}