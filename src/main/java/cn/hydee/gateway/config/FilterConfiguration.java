package cn.hydee.gateway.config;

import cn.hydee.gateway.filter.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 过滤器配置类
 * 
 * 通过配置属性控制使用同步还是响应式过滤器
 * 
 * 配置项：
 * - gateway.filter.reactive.enabled=true  启用响应式过滤器（推荐）
 * - gateway.filter.reactive.enabled=false 使用原有同步过滤器
 */
@Slf4j
@Configuration
public class FilterConfiguration {

    /**
     * 响应式权限校验过滤器
     * 当 gateway.filter.reactive.enabled=true 时启用
     */
    @Bean
    @ConditionalOnProperty(name = "gateway.filter.reactive.enabled", havingValue = "true", matchIfMissing = false)
    public ReactiveAccessGatewayFilter reactiveAccessGatewayFilter() {
        log.info("启用响应式权限校验过滤器 (ReactiveAccessGatewayFilter)");
        return new ReactiveAccessGatewayFilter();
    }

    /**
     * 响应式登录权限校验过滤器工厂
     * 当 gateway.filter.reactive.enabled=true 时启用
     */
    @Bean
    @ConditionalOnProperty(name = "gateway.filter.reactive.enabled", havingValue = "true", matchIfMissing = false)
    public ReactiveYxtLoginAccessGatewayFilterFactory reactiveYxtLoginAccessGatewayFilterFactory() {
        log.info("启用响应式登录权限校验过滤器工厂 (ReactiveYxtLoginAccessGatewayFilterFactory)");
        return new ReactiveYxtLoginAccessGatewayFilterFactory();
    }

    /**
     * 响应式防重提交过滤器
     * 当 gateway.filter.reactive.enabled=true 时启用
     */
    @Bean
    @ConditionalOnProperty(name = "gateway.filter.reactive.enabled", havingValue = "true", matchIfMissing = false)
    public ReactiveIdempotentFilter reactiveIdempotentFilter() {
        log.info("启用响应式防重提交过滤器 (ReactiveIdempotentFilter)");
        return new ReactiveIdempotentFilter();
    }

    /**
     * 传统同步权限校验过滤器
     * 当 gateway.filter.reactive.enabled=false 或未配置时启用
     */
    @Bean
    @ConditionalOnProperty(name = "gateway.filter.reactive.enabled", havingValue = "false", matchIfMissing = true)
    public AccessGatewayFilter accessGatewayFilter() {
        log.info("启用传统同步权限校验过滤器 (AccessGatewayFilter)");
        return new AccessGatewayFilter();
    }

    /**
     * 传统同步登录权限校验过滤器工厂
     * 当 gateway.filter.reactive.enabled=false 或未配置时启用
     */
    @Bean
    @ConditionalOnProperty(name = "gateway.filter.reactive.enabled", havingValue = "false", matchIfMissing = true)
    public YxtLoginAccessGatewayFilterFactory yxtLoginAccessGatewayFilterFactory() {
        log.info("启用传统同步登录权限校验过滤器工厂 (YxtLoginAccessGatewayFilterFactory)");
        return new YxtLoginAccessGatewayFilterFactory();
    }

    /**
     * 传统同步防重提交过滤器
     * 当 gateway.filter.reactive.enabled=false 或未配置时启用
     */
    @Bean
    @ConditionalOnProperty(name = "gateway.filter.reactive.enabled", havingValue = "false", matchIfMissing = true)
    public IdempotentFilter idempotentFilter() {
        log.info("启用传统同步防重提交过滤器 (IdempotentFilter)");
        return new IdempotentFilter();
    }
}
