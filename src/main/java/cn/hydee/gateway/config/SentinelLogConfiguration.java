package cn.hydee.gateway.config;

import com.alibaba.csp.sentinel.log.LogTarget;
import com.alibaba.csp.sentinel.log.RecordLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Sentinel日志配置
 * 
 * 优化Sentinel日志处理，减少阻塞调用对响应式流的影响
 * 
 * <AUTHOR> (moatkon)
 * @date 2025-06-03
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "spring.cloud.sentinel.enabled", havingValue = "true", matchIfMissing = true)
public class SentinelLogConfiguration {

    @PostConstruct
    public void configureSentinelLogging() {
        try {
            // 配置Sentinel日志级别，减少不必要的日志输出
            Logger sentinelLogger = Logger.getLogger("com.alibaba.csp.sentinel");
            sentinelLogger.setLevel(Level.WARNING);
            
            // 配置RecordLog的日志级别
            Logger recordLogger = Logger.getLogger("com.alibaba.csp.sentinel.log.RecordLog");
            recordLogger.setLevel(Level.WARNING);
            
            // 配置节点相关日志级别
            Logger nodeLogger = Logger.getLogger("com.alibaba.csp.sentinel.node");
            nodeLogger.setLevel(Level.WARNING);
            
            // 配置上下文相关日志级别
            Logger contextLogger = Logger.getLogger("com.alibaba.csp.sentinel.context");
            contextLogger.setLevel(Level.WARNING);
            
            log.info("Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用");
            
        } catch (Exception e) {
            log.warn("配置Sentinel日志时发生异常", e);
        }
    }
}
