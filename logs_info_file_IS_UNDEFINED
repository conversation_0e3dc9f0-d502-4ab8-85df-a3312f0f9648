2025-06-03 14:13:24.159|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-03 14:13:24.182|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 48628 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-03 14:13:24.183|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: test
2025-06-03 14:13:25.536|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-03 14:13:25.539|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-03 14:13:25.576|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-06-03 14:13:25.761|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-03 14:13:25.763|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-03 14:13:26.027|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=91399433-ae85-3e7e-a139-6d43cf2a5acc
2025-06-03 14:13:26.103|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-03 14:13:26.106|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-03 14:13:34.314|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:13:38.667|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:14:01.805|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-03 14:14:01.825|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 32400 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-03 14:14:01.826|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-03 14:14:03.182|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-03 14:14:03.187|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-03 14:14:03.223|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-06-03 14:14:03.409|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-03 14:14:03.413|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-03 14:14:03.627|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=91399433-ae85-3e7e-a139-6d43cf2a5acc
2025-06-03 14:14:03.707|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-03 14:14:03.711|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-03 14:14:11.933|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:14:15.978|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:14:20.006|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:14:24.045|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:14:24.046|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-03 14:14:28.072|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:14:32.106|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:14:32.106|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-03 14:14:32.154|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-03 14:14:32.593|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:14:32.599|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:14:32.627|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:14:32.761|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$5407b12] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:14:32.779|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:14:32.780|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:14:32.782|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:14:36.157|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:14:40.191|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:14:44.193|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:14:48.195|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:14:52.198|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:14:53.680|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-03 14:14:53.680|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-03 14:14:53.680|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-03 14:14:53.681|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-03 14:14:53.681|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-03 14:14:53.681|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-03 14:14:53.681|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-03 14:14:53.682|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-03 14:14:53.683|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-03 14:14:53.683|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-03 14:14:53.683|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-03 14:14:53.684|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-03 14:14:53.684|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-03 14:14:53.955|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:14:54.039|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:14:54.118|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:14:54.183|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:14:54.226|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:14:54.255|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:14:54.342|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-03 14:14:54.343|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-03 14:14:54.354|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-03 14:14:54.354|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-03 14:14:54.947|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-03 14:14:54.949|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-03 14:14:55.145|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-03 14:14:55.322|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-03 14:14:55.448|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-03 14:14:55.957|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-03 14:14:56.138|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-03 14:14:56.200|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:14:56.710|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-03 14:14:56.981|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.444 seconds (JVM running for 59.466)
2025-06-03 14:14:56.982|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-03 14:14:56.982|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-03 14:14:56.987|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-03 14:14:57.049|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-03 14:15:16.202|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:15:18.397|INFO| N/A||main|o.s.b.a.l.ConditionEvaluationReportLoggingListener:136|

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-03 14:15:18.449|ERROR| N/A||main|o.s.boot.SpringApplication:860|Application run failed
java.lang.IllegalStateException: Failed to execute ApplicationRunner
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:802)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:789)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:346)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at cn.hydee.gateway.GateWayApplication.main(GateWayApplication.java:26)
Caused by: com.yxt.lang.exception.YxtBizException: Connection timed out: connect executing GET http://yxt-safe-center/api/interface/listByApplicationName?applicationName=10.4.1.212
	at com.yxt.safecenter.auth.sdk.service.SafeCenterDataService.refreshData(SafeCenterDataService.java:83)
	at com.yxt.safecenter.auth.sdk.core.DataLoader.run(DataLoader.java:23)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:799)
	... 5 common frames omitted
2025-06-03 14:15:21.074|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.085|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.096|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.105|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.117|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.127|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.136|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.146|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.155|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.167|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.178|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.187|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.196|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.205|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.215|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.228|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.239|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.247|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.256|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.267|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.276|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.286|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.298|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.306|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.317|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.325|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.335|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.345|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.355|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.364|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.372|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.381|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.390|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.400|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.408|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.417|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.426|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.434|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.444|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.453|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.463|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.474|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.483|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.490|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.500|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.511|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.519|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.528|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.537|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.545|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.553|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.561|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.569|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.577|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.586|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.594|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.603|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.612|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.621|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.630|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.640|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.649|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.658|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.667|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.675|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.684|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.692|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.703|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.714|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.723|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.732|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.742|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.750|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.760|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.768|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.777|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.786|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.794|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.802|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.810|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.818|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.826|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.835|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.844|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.853|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.907|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
2025-06-03 14:15:21.909|ERROR| N/A||scheduling-1|DataLoader:67|#$$查询应用配置数据失败：
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:658)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:638)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:564)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:944)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.cloud.context.named.NamedContextFactory.createContext(NamedContextFactory.java:133)
	at org.springframework.cloud.context.named.NamedContextFactory.getContext(NamedContextFactory.java:101)
	at org.springframework.cloud.context.named.NamedContextFactory.getInstances(NamedContextFactory.java:181)
	at org.springframework.cloud.openfeign.loadbalancer.FeignBlockingLoadBalancerClient.execute(FeignBlockingLoadBalancerClient.java:85)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:119)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy187.enableAppConfigPage(Unknown Source)
	at com.yxt.safecenter.auth.sdk.service.SafeCenterDataService.lambda$refreshData$1(SafeCenterDataService.java:103)
	at com.yxt.safecenter.auth.sdk.utils.PageUtils.pageQueryALL(PageUtils.java:28)
	at com.yxt.safecenter.auth.sdk.service.SafeCenterDataService.refreshData(SafeCenterDataService.java:96)
	at com.yxt.safecenter.auth.sdk.job.SafeCenterDataRefreshJob.executeTask(SafeCenterDataRefreshJob.java:25)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 39 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:564)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:233)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1273)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1257)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveBean(DefaultListableBeanFactory.java:494)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveBean(DefaultListableBeanFactory.java:500)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:349)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:342)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1172)
	at org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplierBuilder.lambda$withDiscoveryClient$1(ServiceInstanceListSupplierBuilder.java:91)
	at org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplierBuilder.build(ServiceInstanceListSupplierBuilder.java:260)
	at org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration.discoveryClientServiceInstanceListSupplier(GreyLoadBalancerClientConfiguration.java:52)
	at sun.reflect.GeneratedMethodAccessor123.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 40 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:610)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:410)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:564)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1598)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1562)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1338)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 63 common frames omitted
Caused by: org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
	at org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor.bind(ConfigurationPropertiesBindingPostProcessor.java:92)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor.postProcessBeforeInitialization(ConfigurationPropertiesBindingPostProcessor.java:78)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:422)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1778)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:602)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$ShortcutDependencyDescriptor.resolveShortcut(AutowiredAnnotationBeanPostProcessor.java:819)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1312)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.resolvedCachedArgument(AutowiredAnnotationBeanPostProcessor.java:601)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.access$000(AutowiredAnnotationBeanPostProcessor.java:131)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:632)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1413)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	... 85 common frames omitted
Caused by: java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@75839695 has been closed already
	at org.springframework.context.support.AbstractApplicationContext.assertBeanFactoryActive(AbstractApplicationContext.java:1138)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1159)
	at org.springframework.boot.context.properties.BoundConfigurationProperties.get(BoundConfigurationProperties.java:78)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBinder.getHandler(ConfigurationPropertiesBinder.java:128)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBinder.getBindHandler(ConfigurationPropertiesBinder.java:110)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBinder.bind(ConfigurationPropertiesBinder.java:90)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor.bind(ConfigurationPropertiesBindingPostProcessor.java:89)
	... 104 common frames omitted
2025-06-03 14:15:52.205|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 64 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:17:00.208|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:19:04.210|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:19:15.955|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:19:44.196|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:21:08.213|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:23:12.215|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:24:15.947|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:24:44.194|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:25:16.217|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:27:20.220|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:29:15.947|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:29:24.221|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:29:44.194|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:31:28.225|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:33:32.227|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:34:15.946|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:34:44.195|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:35:36.230|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:37:40.233|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:39:15.947|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:39:44.195|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:39:48.196|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:41:52.200|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:43:56.202|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:44:15.949|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:44:44.195|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:46:00.204|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:48:04.207|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:49:15.948|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:49:44.208|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:50:08.210|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:52:12.212|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:52:38.033|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-03 14:52:38.067|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 20756 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-03 14:52:38.068|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-03 14:52:39.764|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-03 14:52:39.768|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-03 14:52:39.803|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-06-03 14:52:39.984|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-03 14:52:39.990|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-03 14:52:40.473|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=91399433-ae85-3e7e-a139-6d43cf2a5acc
2025-06-03 14:52:40.550|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-03 14:52:40.554|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-03 14:52:48.794|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:52:52.826|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:52:56.859|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:53:00.892|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:53:00.893|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-03 14:53:04.937|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:53:08.974|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:53:08.975|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-03 14:53:09.028|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-03 14:53:09.429|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:53:09.434|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:53:09.464|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:53:09.559|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$2c7914de] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:53:09.577|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:53:09.578|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:53:09.580|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:53:12.996|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:53:16.997|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:53:20.999|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:53:25.003|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:53:29.006|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:53:30.538|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-03 14:53:30.538|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-03 14:53:30.538|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-03 14:53:30.539|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-03 14:53:30.539|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-03 14:53:30.539|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-03 14:53:30.539|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-03 14:53:30.539|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-03 14:53:30.540|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-03 14:53:30.540|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-03 14:53:30.540|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-03 14:53:30.540|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-03 14:53:30.540|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-03 14:53:30.805|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:53:30.880|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:53:30.959|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:53:31.031|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:53:31.091|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:53:31.123|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:53:31.205|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-03 14:53:31.205|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-03 14:53:31.214|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-03 14:53:31.214|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-03 14:53:31.851|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-03 14:53:31.856|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-03 14:53:32.095|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-03 14:53:32.289|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-03 14:53:32.435|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-03 14:53:32.925|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-03 14:53:33.008|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:53:33.083|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-03 14:53:33.567|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-03 14:53:33.851|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 57.343 seconds (JVM running for 60.075)
2025-06-03 14:53:33.854|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-03 14:53:33.857|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-03 14:53:33.860|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-03 14:53:33.942|WARN| N/A||main|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-03 14:53:33.946|INFO| N/A||main|o.s.b.a.l.ConditionEvaluationReportLoggingListener:136|

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-03 14:53:33.986|ERROR| N/A||main|o.s.boot.SpringApplication:860|Application run failed
java.lang.IllegalStateException: Failed to execute ApplicationRunner
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:802)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:789)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:346)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at cn.hydee.gateway.GateWayApplication.main(GateWayApplication.java:26)
Caused by: com.yxt.lang.exception.YxtBizException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at com.yxt.safecenter.auth.sdk.service.SafeCenterDataService.refreshData(SafeCenterDataService.java:83)
	at com.yxt.safecenter.auth.sdk.core.DataLoader.run(DataLoader.java:23)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:799)
	... 5 common frames omitted
2025-06-03 14:53:40.984|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-03 14:53:41.001|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 20500 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-03 14:53:41.002|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-03 14:53:42.376|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-03 14:53:42.381|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-03 14:53:42.421|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-06-03 14:53:42.623|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-03 14:53:42.626|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-03 14:53:42.945|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=91399433-ae85-3e7e-a139-6d43cf2a5acc
2025-06-03 14:53:43.032|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-03 14:53:43.037|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-03 14:53:51.299|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:53:55.344|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:53:59.383|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:54:03.422|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:54:03.423|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-03 14:54:07.445|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:54:11.481|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:54:11.482|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-03 14:54:11.533|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-03 14:54:11.969|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:54:11.973|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:54:12.000|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:54:12.119|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$7f842211] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:54:12.138|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:54:12.139|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:54:12.141|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:54:15.502|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:54:19.504|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:54:23.507|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:54:27.509|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:54:31.511|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:54:32.965|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-03 14:54:32.965|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-03 14:54:32.965|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-03 14:54:32.965|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-03 14:54:32.966|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-03 14:54:32.966|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-03 14:54:32.966|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-03 14:54:32.966|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-03 14:54:32.966|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-03 14:54:32.966|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-03 14:54:32.967|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-03 14:54:32.967|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-03 14:54:32.967|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-03 14:54:33.237|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:54:33.317|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:54:33.390|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:54:33.458|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:54:33.510|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:54:33.538|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:54:33.619|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-03 14:54:33.619|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-03 14:54:33.633|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-03 14:54:33.634|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-03 14:54:34.390|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-03 14:54:34.394|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-03 14:54:34.580|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-03 14:54:34.761|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-03 14:54:34.912|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-03 14:54:35.465|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-03 14:54:35.514|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:54:35.650|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-03 14:54:36.166|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-03 14:54:36.432|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.645 seconds (JVM running for 59.379)
2025-06-03 14:54:36.433|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-03 14:54:36.434|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-03 14:54:36.438|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-03 14:54:36.496|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-03 14:54:55.517|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:54:57.849|INFO| N/A||main|o.s.b.a.l.ConditionEvaluationReportLoggingListener:136|

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-03 14:54:57.899|ERROR| N/A||main|o.s.boot.SpringApplication:860|Application run failed
java.lang.IllegalStateException: Failed to execute ApplicationRunner
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:802)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:789)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:346)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at cn.hydee.gateway.GateWayApplication.main(GateWayApplication.java:26)
Caused by: com.yxt.lang.exception.YxtBizException: Connection timed out: connect executing GET http://yxt-safe-center/api/interface/listByApplicationName?applicationName=**********
	at com.yxt.safecenter.auth.sdk.service.SafeCenterDataService.refreshData(SafeCenterDataService.java:83)
	at com.yxt.safecenter.auth.sdk.core.DataLoader.run(DataLoader.java:23)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:799)
	... 5 common frames omitted
2025-06-03 14:55:06.051|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.061|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.073|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.083|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.094|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.105|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.116|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.137|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.145|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.161|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.171|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.181|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.189|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.199|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.208|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.218|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.227|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.235|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.245|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.256|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.271|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.282|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.294|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.307|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.318|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.329|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.339|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.349|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.360|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.371|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.381|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.390|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.400|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.408|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.418|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.426|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.435|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.443|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.452|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.464|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.474|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.483|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.496|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.506|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.516|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.525|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.534|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.545|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.555|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.564|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.575|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.583|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.593|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.601|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.609|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.616|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.626|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.634|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.642|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.649|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.657|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.665|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.673|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.682|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.694|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.704|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.714|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.722|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.732|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.742|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.753|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.764|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.774|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.785|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.794|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.804|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.813|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.820|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.829|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.836|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.846|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.853|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.862|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.869|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.878|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.886|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.941|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
2025-06-03 14:55:06.942|ERROR| N/A||scheduling-1|DataLoader:67|#$$查询应用配置数据失败：
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:658)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:638)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:564)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:944)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.cloud.context.named.NamedContextFactory.createContext(NamedContextFactory.java:133)
	at org.springframework.cloud.context.named.NamedContextFactory.getContext(NamedContextFactory.java:101)
	at org.springframework.cloud.context.named.NamedContextFactory.getInstances(NamedContextFactory.java:181)
	at org.springframework.cloud.openfeign.loadbalancer.FeignBlockingLoadBalancerClient.execute(FeignBlockingLoadBalancerClient.java:85)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:119)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy187.enableAppConfigPage(Unknown Source)
	at com.yxt.safecenter.auth.sdk.service.SafeCenterDataService.lambda$refreshData$1(SafeCenterDataService.java:103)
	at com.yxt.safecenter.auth.sdk.utils.PageUtils.pageQueryALL(PageUtils.java:28)
	at com.yxt.safecenter.auth.sdk.service.SafeCenterDataService.refreshData(SafeCenterDataService.java:96)
	at com.yxt.safecenter.auth.sdk.job.SafeCenterDataRefreshJob.executeTask(SafeCenterDataRefreshJob.java:25)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 39 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'reactiveCompositeDiscoveryClient' defined in class path resource [org/springframework/cloud/client/discovery/composite/reactive/ReactiveCompositeDiscoveryClientAutoConfiguration.class]: Unsatisfied dependency expressed through method 'reactiveCompositeDiscoveryClient' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:564)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:233)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1273)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1257)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveBean(DefaultListableBeanFactory.java:494)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveBean(DefaultListableBeanFactory.java:500)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:349)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:342)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1172)
	at org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplierBuilder.lambda$withDiscoveryClient$1(ServiceInstanceListSupplierBuilder.java:91)
	at org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplierBuilder.build(ServiceInstanceListSupplierBuilder.java:260)
	at org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration.discoveryClientServiceInstanceListSupplier(GreyLoadBalancerClientConfiguration.java:52)
	at sun.reflect.GeneratedMethodAccessor124.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 40 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.client.discovery.simple.reactive.SimpleReactiveDiscoveryClientAutoConfiguration': Initialization of bean failed; nested exception is org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:610)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:410)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:564)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1598)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1562)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1451)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1338)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 63 common frames omitted
Caused by: org.springframework.boot.context.properties.ConfigurationPropertiesBindException: Error creating bean with name 'server-org.springframework.boot.autoconfigure.web.ServerProperties': Could not bind properties to 'ServerProperties' : prefix=server, ignoreInvalidFields=false, ignoreUnknownFields=true; nested exception is java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
	at org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor.bind(ConfigurationPropertiesBindingPostProcessor.java:92)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor.postProcessBeforeInitialization(ConfigurationPropertiesBindingPostProcessor.java:78)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:422)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1778)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:602)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$ShortcutDependencyDescriptor.resolveShortcut(AutowiredAnnotationBeanPostProcessor.java:819)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1312)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.resolvedCachedArgument(AutowiredAnnotationBeanPostProcessor.java:601)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.access$000(AutowiredAnnotationBeanPostProcessor.java:131)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:632)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1413)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	... 85 common frames omitted
Caused by: java.lang.IllegalStateException: org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@7afbf2a0 has been closed already
	at org.springframework.context.support.AbstractApplicationContext.assertBeanFactoryActive(AbstractApplicationContext.java:1138)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1159)
	at org.springframework.boot.context.properties.BoundConfigurationProperties.get(BoundConfigurationProperties.java:78)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBinder.getHandler(ConfigurationPropertiesBinder.java:128)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBinder.getBindHandler(ConfigurationPropertiesBinder.java:110)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBinder.bind(ConfigurationPropertiesBinder.java:90)
	at org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor.bind(ConfigurationPropertiesBindingPostProcessor.java:89)
	... 104 common frames omitted
2025-06-03 14:55:55.257|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-03 14:55:55.277|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 49116 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-03 14:55:55.278|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-03 14:55:56.584|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-03 14:55:56.588|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-03 14:55:56.623|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
2025-06-03 14:55:56.822|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-03 14:55:56.824|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-03 14:55:57.132|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=91399433-ae85-3e7e-a139-6d43cf2a5acc
2025-06-03 14:55:57.223|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-03 14:55:57.227|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-03 14:56:05.441|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:56:09.488|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:56:13.526|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:56:17.557|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:56:17.558|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-03 14:56:21.590|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:56:25.622|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:56:25.623|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-03 14:56:25.679|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-03 14:56:26.160|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:56:26.164|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:56:26.196|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:56:26.314|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$79fe4ac4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:56:26.335|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:56:26.336|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:56:26.339|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-03 14:56:29.640|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:56:33.650|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:56:37.652|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:56:41.653|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:56:45.656|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:56:47.036|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-03 14:56:47.036|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-03 14:56:47.036|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-03 14:56:47.037|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-03 14:56:47.037|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-03 14:56:47.037|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-03 14:56:47.037|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-03 14:56:47.037|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-03 14:56:47.037|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-03 14:56:47.038|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-03 14:56:47.038|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-03 14:56:47.038|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-03 14:56:47.038|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-03 14:56:47.286|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:56:47.359|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:56:47.430|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:56:47.492|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:56:47.538|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:56:47.564|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-03 14:56:47.635|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-03 14:56:47.635|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-03 14:56:47.644|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-03 14:56:47.644|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-03 14:56:48.218|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-03 14:56:48.221|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-03 14:56:48.414|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-03 14:56:48.572|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-03 14:56:48.694|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-03 14:56:49.191|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-03 14:56:49.355|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-03 14:56:49.657|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-03 14:56:49.841|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-03 14:56:50.118|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.006 seconds (JVM running for 58.91)
2025-06-03 14:56:50.119|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-03 14:56:50.120|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-03 14:56:50.123|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-03 14:56:50.184|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
