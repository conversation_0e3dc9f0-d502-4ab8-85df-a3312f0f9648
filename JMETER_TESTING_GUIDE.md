# JMeter 性能测试指南

## 🎯 测试目标

使用 JMeter 对 Spring Cloud Gateway 进行性能测试，验证响应式过滤器优化效果。

## 📋 测试准备

### 1. 环境要求
- JMeter 5.6.3 (已安装在 `D:\software\apache-jmeter-5.6.3`)
- Spring Cloud Gateway 应用运行在 `localhost:10002`
- Redis 服务正常运行

### 2. 测试计划文件
项目根目录下的 `jmeter-test-plan.jmx` 包含了完整的测试计划。

## 🚀 执行测试

### 方法一：GUI 模式（推荐用于调试）

1. **启动 JMeter**
   ```bash
   cd D:\software\apache-jmeter-5.6.3\bin
   jmeter.bat
   ```

2. **打开测试计划**
   - File → Open → 选择项目根目录下的 `jmeter-test-plan.jmx`

3. **配置参数**
   - 检查 `gateway_host` 和 `gateway_port` 变量
   - 默认配置：`localhost:10002`

4. **执行测试**
   - 选择要运行的线程组（基准测试或高并发测试）
   - 点击绿色播放按钮开始测试

### 方法二：命令行模式（推荐用于正式测试）

1. **基准测试（100并发）**
   ```bash
   cd D:\software\apache-jmeter-5.6.3\bin
   jmeter -n -t D:\github\codes\businesses-gateway\jmeter-test-plan.jmx -l results-baseline.jtl -e -o baseline-report
   ```

2. **高并发测试（500并发）**
   ```bash
   # 先修改测试计划启用高并发线程组
   jmeter -n -t D:\github\codes\businesses-gateway\jmeter-test-plan.jmx -l results-highload.jtl -e -o highload-report
   ```

## 📊 测试场景

### 场景1：基准测试
- **并发用户**: 100
- **循环次数**: 100
- **总请求数**: 10,000
- **启动时间**: 10秒
- **目标**: 验证基本性能指标

### 场景2：高并发测试
- **并发用户**: 500
- **循环次数**: 50
- **总请求数**: 25,000
- **启动时间**: 30秒
- **目标**: 验证高并发场景下的性能表现

## 🔧 测试配置

### HTTP 请求配置
```
URL: http://localhost:10002/api/test/performance
Method: GET
Headers:
  - Authorization: Bearer test-token-${__threadNum}
  - userId: user-${__threadNum}
  - merCode: TEST001
```

### 关键监控指标
1. **响应时间**
   - 平均响应时间
   - 95% 响应时间
   - 99% 响应时间

2. **吞吐量**
   - QPS (每秒请求数)
   - TPS (每秒事务数)

3. **错误率**
   - 成功率
   - 错误类型分布

## 📈 结果分析

### 查看测试报告

1. **GUI 模式**
   - 汇总报告：显示平均响应时间、吞吐量等
   - 图形结果：显示响应时间趋势图
   - 察看结果树：查看具体请求响应详情

2. **命令行模式**
   - 自动生成 HTML 报告在指定目录
   - 包含详细的性能分析图表

### 性能对比方法

1. **优化前测试**
   ```bash
   # 切换到 master 分支（优化前代码）
   git checkout master
   mvn clean compile
   # 启动应用并执行 JMeter 测试
   ```

2. **优化后测试**
   ```bash
   # 切换到优化分支
   git checkout refactor-20250522-网关优化
   mvn clean compile
   # 启动应用并执行 JMeter 测试
   ```

3. **对比分析**
   - 比较两次测试的响应时间
   - 比较吞吐量提升
   - 分析错误率变化

## 🎯 预期结果

基于我们的单元测试结果，预期 JMeter 测试将显示：

### 性能提升指标
- **响应时间**: 降低 50-70%
- **QPS**: 提升 2-5 倍
- **并发处理能力**: 显著提升
- **错误率**: 保持稳定或降低

### 具体数值参考
```
优化前:
- 平均响应时间: ~5-10ms
- QPS: ~1000-2000
- 95% 响应时间: ~20-50ms

优化后:
- 平均响应时间: ~2-5ms
- QPS: ~3000-8000
- 95% 响应时间: ~10-25ms
```

## 🔍 故障排查

### 常见问题

1. **连接被拒绝**
   - 检查网关应用是否正常启动
   - 确认端口 10002 是否可访问

2. **Redis 连接错误**
   - 确保 Redis 服务正常运行
   - 检查 Redis 连接配置

3. **内存不足**
   - 调整 JMeter 堆内存大小
   - 减少并发用户数

### JMeter 性能调优

1. **增加 JMeter 内存**
   ```bash
   # 编辑 jmeter.bat，修改内存设置
   set HEAP=-Xms1g -Xmx4g -XX:MaxMetaspaceSize=256m
   ```

2. **禁用不必要的监听器**
   - 在正式测试时禁用"察看结果树"
   - 只保留必要的结果收集器

3. **使用命令行模式**
   - 避免 GUI 模式的性能开销
   - 使用 `-n` 参数运行非 GUI 模式

## 📝 测试报告模板

### 测试环境
- 操作系统: Windows
- JMeter 版本: 5.6.3
- 网关版本: 1.0.0
- Redis 版本: [版本号]

### 测试结果
| 指标 | 优化前 | 优化后 | 提升倍数 |
|------|--------|--------|----------|
| 平均响应时间 | [数值] | [数值] | [倍数] |
| 95% 响应时间 | [数值] | [数值] | [倍数] |
| QPS | [数值] | [数值] | [倍数] |
| 错误率 | [百分比] | [百分比] | [变化] |

### 结论
[根据测试结果得出的结论和建议]

---

**注意**: 确保在相同的环境条件下进行对比测试，以获得准确的性能对比数据。
