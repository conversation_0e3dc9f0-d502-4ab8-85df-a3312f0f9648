# Spring Cloud Gateway 响应式过滤器使用指南

## 🚀 快速开始

### 1. 启用响应式过滤器

在 `application.yml` 中添加配置：

```yaml
gateway:
  filter:
    reactive:
      enabled: true  # 启用响应式过滤器
```

### 2. 重启应用

重启网关服务，系统将自动使用响应式过滤器。

## 📋 配置说明

### 完整配置示例

```yaml
server:
  port: 10002

spring:
  application:
    name: businesses-gateway
  redis:
    # Redis 配置保持不变
    host: localhost
    port: 6379
    timeout: 1000ms
    lettuce:
      pool:
        max-active: 200
        max-idle: 50
        min-idle: 10

# 网关过滤器配置
gateway:
  filter:
    reactive:
      # 启用响应式过滤器以提升性能
      # true: 使用响应式过滤器（推荐，性能更好）
      # false: 使用传统同步过滤器
      enabled: true
```

## 🔄 切换模式

### 启用响应式模式（推荐）
```yaml
gateway:
  filter:
    reactive:
      enabled: true
```

### 回滚到同步模式
```yaml
gateway:
  filter:
    reactive:
      enabled: false
```

## 📊 监控和验证

### 1. 日志验证

启动时查看日志，确认使用的过滤器类型：

```
# 响应式模式
INFO - 启用响应式权限校验过滤器 (ReactiveAccessGatewayFilter)
INFO - 启用响应式登录权限校验过滤器工厂 (ReactiveYxtLoginAccessGatewayFilterFactory)
INFO - 启用响应式防重提交过滤器 (ReactiveIdempotentFilter)

# 同步模式
INFO - 启用传统同步权限校验过滤器 (AccessGatewayFilter)
INFO - 启用传统同步登录权限校验过滤器工厂 (YxtLoginAccessGatewayFilterFactory)
INFO - 启用传统同步防重提交过滤器 (IdempotentFilter)
```

### 2. 性能监控

关注以下指标：

- **响应时间**: P95、P99 响应时间
- **QPS**: 每秒请求处理量
- **错误率**: 请求失败率
- **Redis 连接**: 连接池使用情况
- **JVM 指标**: 内存使用、GC 情况

### 3. 运行性能测试

```bash
# 运行性能测试
mvn test -Dtest=SimplePerformanceTest

# 查看测试结果
# 期望看到 2-4 倍的性能提升
```

## 🛠️ 故障排查

### 常见问题

#### 1. 配置不生效
**症状**: 修改配置后仍使用旧的过滤器
**解决**: 确保重启应用，检查配置文件路径

#### 2. Redis 连接异常
**症状**: 启动时 Redis 连接失败
**解决**: 检查 Redis 配置和网络连接

#### 3. 性能没有提升
**症状**: 启用响应式后性能没有明显改善
**排查**:
- 确认配置正确生效
- 检查是否有其他性能瓶颈
- 验证测试场景是否合适

### 日志级别调整

```yaml
logging:
  level:
    cn.hydee.gateway.filter: DEBUG  # 查看过滤器详细日志
    org.springframework.data.redis: DEBUG  # 查看 Redis 操作日志
```

## 🔧 高级配置

### Redis 连接池优化

```yaml
spring:
  redis:
    lettuce:
      pool:
        max-active: 200    # 最大连接数
        max-idle: 50       # 最大空闲连接
        min-idle: 10       # 最小空闲连接
        max-wait: 1000ms   # 最大等待时间
    timeout: 1000ms        # 连接超时
```

### 过滤器顺序

过滤器执行顺序（数值越小优先级越高）：

```java
public interface FilterOrder {
    int RouteForbiddenFilter = Integer.MIN_VALUE + 2;
    int BodygzipFilter = Integer.MIN_VALUE + 3;
    int YxtLoginAccessGatewayFilter = Integer.MIN_VALUE + 4;
    int AccessGatewayFilter = Integer.MIN_VALUE + 5;
    int IdempotentFilter = Integer.MIN_VALUE + 10;
    int ModifyBodyGatewayFilter = Integer.MIN_VALUE + 30;
    int DynamicsRequestFilter = 10149;
}
```

## 📈 性能调优建议

### 1. JVM 参数优化

```bash
# 推荐的 JVM 参数
-Xms2g -Xmx2g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+UnlockExperimentalVMOptions
-XX:+UseJVMCICompiler
```

### 2. 连接池调优

根据实际并发量调整 Redis 连接池：

```yaml
spring:
  redis:
    lettuce:
      pool:
        # 高并发场景
        max-active: 500
        max-idle: 100
        min-idle: 20
```

### 3. 监控告警

设置关键指标告警：

- 响应时间 P99 > 100ms
- QPS 下降 > 20%
- 错误率 > 1%
- Redis 连接池使用率 > 80%

## 🚨 注意事项

### 1. 兼容性
- 响应式过滤器与原有过滤器功能完全兼容
- 支持无缝切换，不影响业务逻辑

### 2. 部署建议
- 建议先在测试环境验证
- 生产环境采用灰度发布
- 准备快速回滚方案

### 3. 监控重点
- 启用后密切监控系统指标
- 关注错误日志和异常情况
- 验证业务功能正常

## 📞 技术支持

如遇到问题，请：

1. 检查日志文件
2. 验证配置正确性
3. 运行性能测试
4. 联系技术团队

## 📚 相关文档

- [Spring Cloud Gateway 官方文档](https://spring.io/projects/spring-cloud-gateway)
- [Spring Data Redis Reactive 文档](https://docs.spring.io/spring-data/redis/docs/current/reference/html/#redis:reactive)
- [Project Reactor 文档](https://projectreactor.io/docs)

---

**祝您使用愉快！如有问题请及时反馈。** 🎉
