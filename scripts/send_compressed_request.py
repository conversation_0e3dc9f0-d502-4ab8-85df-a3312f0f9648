#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GZIP压缩请求发送工具

用于向Spring Cloud Gateway发送GZIP压缩的HTTP请求
模拟BodygzipFilter期望的请求格式

使用方法:
    python send_compressed_request.py
    python send_compressed_request.py --url http://localhost:9000/dev-test/forBusinessGateway/1
    python send_compressed_request.py --file test_data.json
"""

import gzip
import json
import argparse
import requests
import sys
from typing import Dict, Any

def compress_json_data(json_data: str) -> bytes:
    """
    压缩JSON数据，使用与Java GZIPUtils相同的方式
    
    Args:
        json_data: 原始JSON字符串
        
    Returns:
        压缩后的字节数据
    """
    # 使用UTF-8编码转换为字节
    json_bytes = json_data.encode('utf-8')
    
    # GZIP压缩
    compressed_data = gzip.compress(json_bytes)
    
    return compressed_data

def send_compressed_request(url: str, json_data: str) -> Dict[str, Any]:
    """
    发送GZIP压缩的HTTP请求
    
    Args:
        url: 目标URL
        json_data: 原始JSON数据
        
    Returns:
        响应信息字典
    """
    try:
        # 1. 压缩数据
        compressed_data = compress_json_data(json_data)
        
        # 2. 转换为ISO-8859-1编码的字符串（模拟Java的处理方式）
        compressed_body = compressed_data.decode('iso-8859-1')
        
        # 3. 设置请求头
        headers = {
            'Content-Type': 'application/json',
            'huditCompressed': '1',  # 关键标识
            'Accept-Encoding': 'gzip',
            'User-Agent': 'Python-GZIPRequestSender/1.0'
        }
        
        # 4. 发送请求
        print(f"发送压缩请求到: {url}")
        print(f"原始数据大小: {len(json_data.encode('utf-8'))} bytes")
        print(f"压缩后大小: {len(compressed_data)} bytes")
        print(f"压缩比: {(1 - len(compressed_data) / len(json_data.encode('utf-8'))) * 100:.2f}%")
        print(f"请求头: {headers}")
        print("---")
        
        # 将压缩后的字符串重新编码为UTF-8发送
        response = requests.post(
            url, 
            data=compressed_body.encode('utf-8'),
            headers=headers,
            timeout=30
        )
        
        return {
            'status_code': response.status_code,
            'headers': dict(response.headers),
            'content': response.text,
            'success': True
        }
        
    except requests.exceptions.RequestException as e:
        return {
            'error': str(e),
            'success': False
        }
    except Exception as e:
        return {
            'error': f"压缩或发送失败: {str(e)}",
            'success': False
        }

def load_json_from_file(file_path: str) -> str:
    """从文件加载JSON数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"读取文件失败: {e}")
        sys.exit(1)

def get_test_data() -> list:
    """获取预定义的测试数据"""
    return [
        {
            "merCode": "TEST001",
            "userId": "user123",
            "action": "login",
            "timestamp": "2025-06-03T15:30:00",
            "message": "这是一个GZIP压缩测试请求"
        },
        {
            "merCode": "HYDEE001", 
            "userId": "user456",
            "action": "order",
            "orderData": {
                "productId": "P001",
                "productName": "测试商品",
                "quantity": 2,
                "price": 99.99,
                "description": "这是一个包含中文的测试商品描述"
            },
            "timestamp": "2025-06-03T15:31:00"
        },
        {
            "merCode": "GATEWAY001",
            "userId": "user789", 
            "action": "query",
            "queryParams": {
                "startDate": "2025-06-01",
                "endDate": "2025-06-03",
                "status": "active",
                "keywords": "测试关键词",
                "filters": ["filter1", "filter2", "filter3"]
            },
            "timestamp": "2025-06-03T15:32:00"
        }
    ]

def generate_large_test_data() -> dict:
    """生成大数据量测试数据"""
    data = {
        "merCode": "LARGE_DATA_TEST",
        "userId": "user_large_test",
        "action": "bulk_data",
        "data": []
    }
    
    # 生成1000条测试数据
    for i in range(1000):
        item = {
            "id": i,
            "name": f"测试数据项_{i}",
            "description": f"这是第{i}条测试数据，用于验证大数据量压缩传输的效果。包含中文字符和特殊符号！@#$%^&*()",
            "value": round(i * 3.14159, 2),
            "timestamp": f"2025-06-03T15:{i%60:02d}:00",
            "tags": [f"tag_{i%10}", f"category_{i%5}", f"type_{i%3}"]
        }
        data["data"].append(item)
    
    data["timestamp"] = "2025-06-03T15:30:00"
    data["summary"] = {
        "total_items": len(data["data"]),
        "generated_at": "2025-06-03T15:30:00",
        "test_purpose": "验证GZIP压缩在大数据量场景下的效果"
    }
    
    return data

def main():
    parser = argparse.ArgumentParser(description='发送GZIP压缩的HTTP请求到Spring Cloud Gateway')
    parser.add_argument('--url', default='http://localhost:9000/dev-test/forBusinessGateway/1',
                       help='目标URL (默认: http://localhost:9000/dev-test/forBusinessGateway/1)')
    parser.add_argument('--file', help='从文件读取JSON数据')
    parser.add_argument('--test', choices=['small', 'large', 'all'], default='small',
                       help='使用预定义测试数据 (small: 小数据, large: 大数据, all: 全部)')
    parser.add_argument('--interactive', action='store_true', help='交互模式')
    
    args = parser.parse_args()
    
    print("=== GZIP压缩请求发送工具 ===")
    print(f"目标URL: {args.url}")
    print()
    
    if args.interactive:
        # 交互模式
        while True:
            print("请选择操作:")
            print("1. 发送小数据量测试请求")
            print("2. 发送大数据量测试请求") 
            print("3. 输入自定义JSON")
            print("4. 退出")
            
            choice = input("请输入选择 (1-4): ").strip()
            
            if choice == '1':
                test_data_list = get_test_data()
                for i, data in enumerate(test_data_list):
                    print(f"\n发送测试请求 {i+1}:")
                    json_str = json.dumps(data, ensure_ascii=False, indent=2)
                    result = send_compressed_request(args.url, json_str)
                    print_result(result)
                    
            elif choice == '2':
                print("\n生成大数据量测试请求...")
                large_data = generate_large_test_data()
                json_str = json.dumps(large_data, ensure_ascii=False, indent=2)
                print(f"大数据量请求生成完成，数据大小: {len(json_str.encode('utf-8'))} bytes")
                result = send_compressed_request(args.url, json_str)
                print_result(result)
                
            elif choice == '3':
                print("请输入JSON数据 (输入空行结束):")
                lines = []
                while True:
                    line = input()
                    if line.strip() == '':
                        break
                    lines.append(line)
                
                if lines:
                    json_str = '\n'.join(lines)
                    try:
                        # 验证JSON格式
                        json.loads(json_str)
                        result = send_compressed_request(args.url, json_str)
                        print_result(result)
                    except json.JSONDecodeError as e:
                        print(f"JSON格式错误: {e}")
                else:
                    print("未输入有效的JSON数据")
                    
            elif choice == '4':
                print("退出程序")
                break
            else:
                print("无效选择，请重新输入")
            
            print()
    
    elif args.file:
        # 从文件读取数据
        json_data = load_json_from_file(args.file)
        result = send_compressed_request(args.url, json_data)
        print_result(result)
        
    else:
        # 使用预定义测试数据
        if args.test in ['small', 'all']:
            test_data_list = get_test_data()
            for i, data in enumerate(test_data_list):
                print(f"发送测试请求 {i+1}:")
                json_str = json.dumps(data, ensure_ascii=False, indent=2)
                result = send_compressed_request(args.url, json_str)
                print_result(result)
                print()
        
        if args.test in ['large', 'all']:
            print("生成大数据量测试请求...")
            large_data = generate_large_test_data()
            json_str = json.dumps(large_data, ensure_ascii=False, indent=2)
            print(f"大数据量请求生成完成，数据大小: {len(json_str.encode('utf-8'))} bytes")
            result = send_compressed_request(args.url, json_str)
            print_result(result)

def print_result(result: Dict[str, Any]):
    """打印请求结果"""
    if result['success']:
        print(f"✅ 请求成功")
        print(f"状态码: {result['status_code']}")
        print(f"响应头: {result['headers']}")
        print(f"响应内容: {result['content']}")
    else:
        print(f"❌ 请求失败: {result['error']}")

if __name__ == '__main__':
    main()
